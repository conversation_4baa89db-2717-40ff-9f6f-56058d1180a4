# AUTOGENERATED FROM build-test-manylinux-aarch64.jsonnet DO NOT MODIFY
jobs:
  build-wheels:
    permissions:
      contents: read
      id-token: write
    runs-on: ubuntu-latest
    steps:
      - uses: docker/setup-qemu-action@v3
      - uses: docker/setup-buildx-action@v3
      - uses: depot/setup-action@v1
      - id: build-semgrep-wheel
        name: Build and test python wheel
        uses: depot/build-push-action@v1.9.0
        with:
          buildx-fallback: true
          outputs: type=docker,dest=/tmp/image.tar
          platforms: linux/arm64
          project: fhmxj6w9z8
          target: semgrep-wheel
      - env:
          IMAGEID: ${{ steps.build-semgrep-wheel.outputs.imageid }}
        name: Extract wheel from docker image
        run: |
          # load the docker image containing the semgrep python wheel
          docker load --input /tmp/image.tar

          # create a new docker container using the image we just loaded
          # note: `docker create` simply prepares the container filesystem -- nothing is executed!
          CONTAINER_ID=$(docker create "$IMAGEID")

          # use `docker export` to extract the python wheel zipfile out of the container
          docker export $CONTAINER_ID | tar xv semgrep/cli/dist.zip

          # clean up after ourselves
          docker rm $CONTAINER_ID

          # note: this was originally accomplished by running `cat` from within the container and redirecting stdout to a file:
          #
          # docker run --platform linux/arm64 --rm "$IMAGEID" cat cli/dist.zip > /tmp/dist.zip
          #
          # we ended up hitting an edge case where the final ~100kb doesn't always get written to disk, which results in a corrupted zip file.
          # our theory is that the pipe between the container and the host is being closed prematurely.
      - uses: actions/upload-artifact@v4
        with:
          name: manylinux-aarch64-wheel
          path: semgrep/cli/dist.zip
name: build-test-manylinux-aarch64
on:
  workflow_call: null
  workflow_dispatch: null
