# AUTO<PERSON>NERATED FROM nightly.jsonnet DO NOT MODIFY
jobs:
  brew-build:
    name: Build Semgrep via Brew from HEAD
    runs-on: macos-latest
    steps:
      - env:
          HOMEBREW_NO_INSTALL_FROM_API: 1
        run: brew update --debug --verbose
      - env:
          HOMEBREW_NO_INSTALL_FROM_API: 1
          NONINTERACTIVE: 1
        run: brew install semgrep --HEAD --debug || brew link --overwrite semgrep
      - env:
          HOMEBREW_NO_INSTALL_FROM_API: 1
        name: Check installed correctly
        run: brew test semgrep --HEAD
  notify-failure:
    if: failure()
    needs:
      - brew-build
    runs-on: ubuntu-22.04
    steps:
      - run: |
          curl --request POST \
           --url  ${{ secrets.NOTIFICATIONS_URL }} \
           --header 'content-type: application/json' \
           --data "{
             \"text\": \"The nightly cron failed on ${{ github.sha }}. See https://github.com/${{github.repository}}/actions/runs/${{github.run_id}} for more information.\"
           }"
name: nightly
on:
  schedule:
    - cron: 26 9 * * *
  workflow_dispatch: null
