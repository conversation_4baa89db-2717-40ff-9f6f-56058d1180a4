# AUTOGENERATED FROM lint.jsonnet DO NOT MODIFY
jobs:
  github-actions:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: "1.19"
      - run: go install github.com/rhysd/actionlint/cmd/actionlint@v1.7.7
      - run: |
          cd .github/workflows
          actionlint -shellcheck=''
  jsonnet-gha:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Check GitHub workflow files are up to date
        run: |
          sudo apt-get update
          sudo apt-get install jsonnet
          cd .github/workflows
          make clean
          make
          git diff --exit-code
  pre-commit:
    runs-on: ubuntu-latest
    steps:
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - uses: semgrep/setup-ocaml@latest
        with:
          cache-prefix: v1
          ocaml-compiler: 5.3.0
          opam-pin: false
          save-opam-post-run: true
      - run: opam install -y ocamlformat.0.27.0
      - uses: pre-commit/action@v3.0.0
name: lint
on:
  pull_request:
    branches-ignore:
      - '**/graphite-base/**'
    types:
      - opened
      - reopened
      - synchronize
  push:
    branches:
      - develop
      - release-*
  workflow_dispatch: null
