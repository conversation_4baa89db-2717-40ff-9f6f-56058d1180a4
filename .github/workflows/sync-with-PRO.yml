# AUTOGENERATED FROM sync-with-PRO.jsonnet DO NOT MODIFY
jobs:
  job:
    permissions:
      contents: write
      id-token: write
    runs-on: ubuntu-latest
    steps:
      - env:
          EXPIRATION: 600
          ISSUER: ${{ secrets.SEMGREP_CI_APP_ID }}
          PRIVATE_KEY: ${{ secrets.SEMGREP_CI_APP_KEY }}
        id: jwt
        name: Get JWT for semgrep-ci GitHub App
        uses: docker://public.ecr.aws/y9k7q4m1/devops/cicd:latest
      - env:
          JWT: ${{ steps.jwt.outputs.jwt }}
          SEMGREP_CI_APP_INSTALLATION_ID: ${{ secrets.SEMGREP_CI_APP_INSTALLATION_ID }}
        id: token
        name: Get token for semgrep-ci GitHub App
        run: |
          TOKEN="$(curl -X POST \
          -H "Authorization: Bearer $JWT" \
          -H "Accept: application/vnd.github.v3+json" \
          "https://api.github.com/app/installations/${SEMGREP_CI_APP_INSTALLATION_ID}/access_tokens" | \
          jq -r .token)"
          echo "::add-mask::$TOKEN"
          echo "token=$TOKEN" >> $GITHUB_OUTPUT
      - name: Checkout OSS
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: develop
          token: ${{ steps.token.outputs.token }}
      - name: Checkout PRO
        uses: actions/checkout@v4
        with:
          path: PRO
          repository: semgrep/semgrep-proprietary
          token: ${{ steps.token.outputs.token }}
      - env:
          BRANCHNAME: sync-with-PRO-${{ github.run_id }}-${{ github.run_attempt }}
          GITHUB_TOKEN: ${{ steps.token.outputs.token }}
        name: Creating the branch and commiting to it
        run: |
          if git show --stat develop | grep -q "synced from Pro"; then
             echo "error: HEAD commit already comes from Pro and cannot be synced"
             exit 1
          fi
          # will generate a 0001-xxx patch
          git format-patch develop^
          OSSREF=`git rev-parse develop`
          cd PRO
          git config --global user.name "GitHub Actions Bot"
          git config --global user.email "<>"
          git checkout -b $BRANCHNAME
          git am --directory=OSS ../0001-*
          git log -1 --pretty=%B >message
          echo "" >>message
          echo "synced from OSS $OSSREF" >>message
          git commit --amend -F message
          git push origin $BRANCHNAME
      - env:
          GITHUB_TOKEN: ${{ steps.token.outputs.token }}
        name: Create the Pull request with gh
        run: |
          cd PRO
          gh pr create --fill --base develop
name: sync-with-PRO
on:
  workflow_dispatch: null
