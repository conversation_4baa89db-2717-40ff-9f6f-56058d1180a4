# AUTOGENERATED FROM build-test-windows-x86.jsonnet DO NOT MODIFY
jobs:
  build-core:
    defaults:
      run:
        shell: bash
    runs-on: windows-latest
    steps:
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - uses: semgrep/setup-ocaml@latest
        with:
          cache-prefix: v1
          ocaml-compiler: 5.3.0
          opam-pin: false
          save-opam-post-run: true
      - name: Install older openssl in Cygwin
        run: |
          PACKAGES='mingw64-x86_64-openssl=1.0.2u+za-1,mingw64-i686-openssl=1.0.2u+za-1'
          CYGWIN_ROOT=$(cygpath -w /)
          $CYGWIN_ROOT/setup-x86_64.exe -P $PACKAGES --quiet-mode -R $CYGWIN_ROOT
      - name: Install flexlink patched to use response files and cygpath -file arg
        run: |
          git clone -b argument-list-too-long https://github.com/punchagan/flexdll.git
          cd flexdll/
          opam exec -- make all MSVC_DETECT=0 CHAINS="mingw64"
          cp flexlink.exe ../_opam/bin/
      - name: Debug stuff
        run: |
          ls
          # to see the bin symlink for example
          ls -l
          set
          # tree-sitter fails to compile without an ar, you can use
          # CC=x86_64-w64-mingw32-gcc but there is no AR=x86_64-w64-mingw32-ar
          which ar
          ar --version
          which ar
          ar --version
          which opam
          # this should be fdopen's opan, so 2.0.10
          opam --version
          opam repo
          # we should be on 4.14.0~mingw
          opam switch
      - env:
          CC: x86_64-w64-mingw32-gcc
        name: Build tree-sitter
        run: |
          cd libs/ocaml-tree-sitter-core
          ./configure
          ./scripts/download-tree-sitter --lazy
          prefix="$(pwd)/tree-sitter"
          cd downloads/tree-sitter
          make PREFIX="$prefix" CFLAGS="-O3 -Wall -Wextra"
          make PREFIX="$prefix" install
      - name: Install OPAM deps
        run: |
          # NOTE: ocurl's ./configure fails with an error finding curl/curl.h.
          # Setting PKG_CONFIG_PATH to $(x86_64-w64-mingw32-gcc
          # -print-sysroot)/mingw/include would set UNIX paths for CFLAG and
          # LDFLAG, but that doesn't work. Setting Windows PATHs for them gets
          # the ocurl build to work. To avoid setting these PATHs for all the
          # package builds, we first try to install all the dependencies, and
          # then install ocurl and later other dependencies that depend on ocurl.
          make install-opam-deps || true
          export CYGWIN_SYS_ROOT="$(x86_64-w64-mingw32-gcc --print-sysroot)"
          CFLAGS="-I$(cygpath -w $CYGWIN_SYS_ROOT/mingw/include)" LDFLAGS="-L$(cygpath -w $CYGWIN_SYS_ROOT/mingw/lib)" opam install -y ocurl.0.9.1
          make install-opam-deps
      - name: Build semgrep-core
        run: |
          export TREESITTER_INCDIR=$(pwd)/libs/ocaml-tree-sitter-core/tree-sitter/include
          export TREESITTER_LIBDIR=$(pwd)/libs/ocaml-tree-sitter-core/tree-sitter/lib
          export TREESITTER_BINDIR=$(pwd)/libs/ocaml-tree-sitter-core/tree-sitter/bin

          # We have to strip rpath from the tree-sitter projects because there's no
          # equivalent in Windows
          # TODO: investigate removing rpath from the tree-sitter projects
          for filename in $(find ./languages/ ./libs/ocaml-tree-sitter-core/ -name dune); do
            grep -v rpath $filename > $filename.new
            mv $filename.new $filename
          done
          opam exec -- dune build _build/install/default/bin/semgrep-core.exe
      - name: Test semgrep-core
        run: |
          treesitter_bindir="$(pwd)/libs/ocaml-tree-sitter-core/tree-sitter/bin"
          export PATH="$treesitter_bindir:$PATH"
          # see pro workflow & semgrep-proprietary/pull/3522
          opam exec -- _build/install/default/bin/semgrep-core.exe -l python -rules tests/windows/rules.yml -json tests/windows/test.py
      - name: Copy bin/semgrep-core.exe DLLs to extra-artifacts/
        run: |
          mkdir -p extra-artifacts
          SYS_ROOT_BIN="$(x86_64-w64-mingw32-gcc -print-sysroot)/mingw/bin"
          # path to tree-sitter.dll
          TREESITTER_BIN=$(pwd)/libs/ocaml-tree-sitter-core/tree-sitter/bin
          DLL_PATHS=$SYS_ROOT_BIN:$TREESITTER_BIN
          dlls=$(PATH=$DLL_PATHS:$PATH cygcheck "bin/semgrep-core.exe" | grep '\(x86_64-w64-mingw32\|ocaml-tree-sitter-core\)' | sed 's/^[[:space:]]*//' | sort -u)
          for dll in $dlls; do
            echo "Copying $dll to extra-artifacts/"
            cp -p "$dll" "extra-artifacts"
          done
      - name: Make artifact for bin/semgrep-core.exe extra-artifacts/*
        run: |
          mkdir artifacts
          cp bin/semgrep-core.exe extra-artifacts/* artifacts/
          tar czf artifacts.tgz artifacts
          # so that we can untar later and not get a message
          # about existing artifacts/ directory
          rm -rf artifacts
      - uses: actions/upload-artifact@v4
        with:
          name: semgrep-core-and-dependent-libs-w64-artifact-${{ github.sha }}
          path: artifacts.tgz
  build-wheels:
    defaults:
      run:
        shell: bash
    needs:
      - build-core
    runs-on: windows-latest
    steps:
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - uses: actions/download-artifact@v4
        with:
          name: semgrep-core-and-dependent-libs-w64-artifact-${{ github.sha }}
      - run: |
          tar xvfz artifacts.tgz
          cp artifacts/* cli/src/semgrep/bin
          ./scripts/build-wheels.sh --plat-name win_amd64
      - uses: actions/upload-artifact@v4
        with:
          name: windows-x86-wheel
          path: cli/dist.tgz
  test-wheels:
    defaults:
      run:
        shell: bash
    needs:
      - build-wheels
    runs-on: windows-latest
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: windows-x86-wheel
      - run: tar xzvf dist.tgz
      - name: install package
        run: pip3 install dist/*.whl
      - name: test package
        run: semgrep --version
      - name: e2e semgrep-core test
        run: echo '1 == 1' | semgrep -l python -e '$X == $X' --strict -
name: build-test-windows-x86
on:
  workflow_call: null
  workflow_dispatch: null
