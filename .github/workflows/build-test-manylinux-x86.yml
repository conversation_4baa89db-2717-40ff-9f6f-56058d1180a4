# AUTOGENERATED FROM build-test-manylinux-x86.jsonnet DO NOT MODIFY
jobs:
  build-wheels:
    container: quay.io/pypa/manylinux_2_28_x86_64
    runs-on: ubuntu-latest
    steps:
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - run: |
          yum update -y
          yum install -y zip python3-pip python3.9
          alternatives --remove-all python3
          alternatives --install /usr/bin/python3 python3 /usr/bin/python3.9 1
          alternatives --auto python3
      - uses: actions/download-artifact@v4
        with:
          name: semgrep-core-x86-artifact
      - run: |
          tar xf artifacts.tgz
          cp artifacts/semgrep-core cli/src/semgrep/bin
          ./scripts/build-wheels.sh
      - uses: actions/upload-artifact@v4
        with:
          name: manylinux-x86-wheel
          path: cli/dist.zip
  test-wheels:
    container: quay.io/pypa/manylinux_2_28_x86_64
    needs:
      - build-wheels
    runs-on: ubuntu-latest
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: manylinux-x86-wheel
      - run: unzip dist.zip
      - name: install package
        run: /opt/python/cp39-cp39/bin/pip install dist/*.whl
      - name: test package
        run: |
          export PATH=/opt/python/cp39-cp39/bin:$PATH
          semgrep --version
      - name: e2e semgrep-core test
        run: |
          export PATH=/opt/python/cp39-cp39/bin:$PATH
          echo '1 == 1' | semgrep -l python -e '$X == $X' -
  test-wheels-venv:
    container: quay.io/pypa/manylinux_2_28_x86_64
    needs:
      - build-wheels
    runs-on: ubuntu-latest
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: manylinux-x86-wheel
      - run: unzip dist.zip
      - name: create venv
        run: /opt/python/cp39-cp39/bin/python3 -m venv env
      - name: install package
        run: env/bin/pip install dist/*.whl
      - name: test package
        run: |
          env/bin/semgrep --version
      - name: e2e semgrep-core test
        run: |
          echo '1 == 1' | env/bin/semgrep -l python -e '$X == $X' -
  test-wheels-wsl:
    needs:
      - build-wheels
    runs-on: windows-latest
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: manylinux-x86-wheel
      - run: unzip dist.zip
      - uses: Vampire/setup-wsl@v3
      - name: Install Python
        run: |
          sudo apt update -y
          sudo apt install -y make python3 python3-pip
          sudo ln -s /usr/bin/python3 /usr/bin/python
        shell: wsl-bash {0}
      - name: install package
        run: python3 -m pip install dist/*.whl
        shell: wsl-bash {0}
      - name: test package
        run: |
          semgrep --version
        shell: wsl-bash {0}
      - name: e2e semgrep-core test
        run: |
          echo '1 == 1' | semgrep -l python -e '$X == $X' -
        shell: wsl-bash {0}
name: build-test-manylinux-x86
on:
  workflow_call: null
  workflow_dispatch: null
