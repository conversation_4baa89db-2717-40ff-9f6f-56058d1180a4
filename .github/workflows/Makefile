# Makefile to automatically convert the .jsonnet GHA workflows
# to .yml (GHA accepts only the YAML format).
#
# To convert more yml files to jsonnet, install 'yq' and 'jsonnetfmt'
# and run `jsonnetfmt <(cat foo.yml | yq -p yml -o json)` to get a first draft.

LIBS = \
  libs/actions.libsonnet \
  libs/gha.libsonnet \
  libs/semgrep.libsonnet \
  libs/docker.libsonnet

OBJS = \
  semgrep.yml lint.yml \
  build-test-core-x86.yml \
  build-test-osx-x86.yml build-test-osx-arm64.yml \
  build-test-manylinux-x86.yml build-test-manylinux-aarch64.yml \
  build-test-windows-x86.yml \
  tests.yml \
  test-e2e-semgrep-ci.yml nightly.yml \
  build-test-docker.yml \
  trigger-semgrep-comparison-argo.yml \
  sync-with-PRO.yml

#TODO: in theory we should have a 'make depend' as modifying some .jsonnet
# actually requires then to regenerate other files that depends on this .jsonnet
# The brute force way to make sure everything is ok (which is actually used
# in CI) is to 'make clean; make' each time.
all: $(OBJS)

%.yml: %.jsonnet $(LIBS)
	echo "# AUTOGENERATED FROM $< DO NOT MODIFY" > $@
	../../scripts/jsonnet_to_yaml.sh $< >> $@ || { rm -f $@; exit 1; }

clean:
	rm -f $(OBJS)

# for pad's codemap
.PHONY: gen-codemapignore
gen-codemapignore: $(OBJS)
	rm -f .codemapignore
	for i in $(OBJS);  do \
		echo $$i >> .codemapignore; \
	done
