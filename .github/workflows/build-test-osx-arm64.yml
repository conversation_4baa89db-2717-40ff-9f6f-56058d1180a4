# AUTOGENERATED FROM build-test-osx-arm64.jsonnet DO NOT MODIFY
jobs:
  build-core:
    runs-on: macos-latest
    steps:
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - uses: semgrep/setup-ocaml@latest
        with:
          cache-prefix: v1
          ocaml-compiler: 5.3.0
          opam-pin: false
          save-opam-post-run: true
      - name: Install dependencies
        run: opam exec -- make install-deps
      - name: Build semgrep-core
        run: opam exec -- make
      - name: Test semgrep-core
        run: opam exec -- make test
      - name: Make artifact for ./bin/semgrep-core
        run: |
          mkdir artifacts
          cp ./bin/semgrep-core artifacts/
          tar czf artifacts.tgz artifacts
          # so that we can untar later and not get a message
          # about existing artifacts/ directory
          rm -rf artifacts
      - uses: actions/upload-artifact@v4
        with:
          name: semgrep-osx-arm64-${{ github.sha }}
          path: artifacts.tgz
  build-wheels:
    needs:
      - build-core
    runs-on: macos-latest
    steps:
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - uses: actions/setup-python@v5
        with:
          python-version: "3.12"
      - uses: actions/download-artifact@v4
        with:
          name: semgrep-osx-arm64-${{ github.sha }}
      - run: |
          tar xvfz artifacts.tgz
          cp artifacts/semgrep-core cli/src/semgrep/bin
          ./scripts/build-wheels.sh --plat-name macosx_11_0_arm64
      - uses: actions/upload-artifact@v4
        with:
          name: osx-arm64-wheel
          path: cli/dist.zip
  test-wheels:
    needs:
      - build-wheels
    runs-on: macos-latest
    steps:
      - uses: actions/setup-python@v5
        with:
          python-version: "3.12"
      - uses: actions/download-artifact@v4
        with:
          name: osx-arm64-wheel
      - run: unzip dist.zip
      - name: install package
        run: pip3 install dist/*.whl
      - run: semgrep --version
      - name: e2e semgrep-core test
        run: echo '1 == 1' | semgrep -l python -e '$X == $X' -
      - name: test dynamically linked libraries are in /usr/lib/
        run: |
          otool -L $(semgrep --dump-engine-path) | tee otool.txt
          if [ $? -ne 0 ]; then
             echo "Failed to list dynamically linked libraries.";
             exit 1;
          fi
          NON_USR_LIB_DYNAMIC_LIBRARIES=$(tail -n +2 otool.txt | grep -v "^\\s*/usr/lib/")
          if [ $? -eq 0 ]; then
             echo "Error: semgrep-core has been dynamically linked against libraries outside /usr/lib:"
             echo $NON_USR_LIB_DYNAMIC_LIBRARIES
             exit 1;
          fi;
        shell: bash {0}
name: build-test-osx-arm64
on:
  workflow_call: null
  workflow_dispatch: null
