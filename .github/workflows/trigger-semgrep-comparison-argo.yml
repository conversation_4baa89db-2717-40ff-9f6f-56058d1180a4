# AUTOGENERATED FROM trigger-semgrep-comparison-argo.jsonnet DO NOT MODIFY
jobs:
  get-sha:
    name: Get SH<PERSON> of branch head for attaching Argo Workflow checks
    outputs:
      sha: ${{ steps.get-sha.outputs.sha }}
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - env:
          BRANCH: ${{ github.event.inputs.branch }}
        id: get-sha
        run: |
          echo "Branch: $BRANCH"
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "sha=${{ github.event.pull_request.head.sha }}" >> "$GITHUB_OUTPUT"
          elif [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            git fetch origin $BRANCH
            SHA=$(git rev-parse origin/$BRANCH)
            echo "sha=$SHA" >> "$GITHUB_OUTPUT"
          fi
  setup-docker-tag:
    name: Reconstruct Docker tag created by docker/metadata-action
    outputs:
      docker-tag: ${{ steps.setup-docker-tag.outputs.docker-tag }}
    runs-on: ubuntu-22.04
    steps:
      - id: setup-docker-tag
        run: |
          echo "Github event is ${{ github.event_name }}"
          if [ "${{ github.repository }}" = "semgrep/semgrep-proprietary" ]; then
            PRO_PREFIX="pro-"
          else
            PRO_PREFIX=""
          fi
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "docker-tag=${PRO_PREFIX}pr-${{ github.event.pull_request.number }}" >> "$GITHUB_OUTPUT"
            echo "Setting docker tag to current pull request number"
          else
            echo "docker-tag=${PRO_PREFIX}develop" >> "$GITHUB_OUTPUT"
            echo "Setting dry-run to develop"
          fi
  trigger-semgrep-comparison-argo-workflow:
    needs:
      - setup-docker-tag
      - get-sha
    runs-on: ubuntu-22.04
    steps:
      - env:
          CONTAINER_IMAGE_BASE: ${{ needs.setup-docker-tag.outputs.docker-tag == 'develop' && 'latest' || 'develop' }}
          CONTAINER_IMAGE_DEVELOPMENT: ${{ needs.setup-docker-tag.outputs.docker-tag }}
          ISSUE_NUMBER: ${{ github.event.pull_request.number }}
          REPOSITORY: ${{ github.repository }}
          RULESET: p/default-v2
          SHA: ${{ needs.get-sha.outputs.sha }}
          TIMEOUT_IN_MINUTES: "12"
          TOKEN: ${{ secrets.ARGO_WORKFLOWS_TOKEN }}
        id: trigger
        run: |
          echo "Repository: $REPOSITORY"
          echo "SHA: $SHA"
          curl --fail-with-body -X POST https://argoworkflows-dev2.corp.semgrep.dev/api/v1/events/security-research/semgrep-compare \
            -H "Authorization: Bearer $TOKEN" \
            -d "{\"container_image_base\": \"$CONTAINER_IMAGE_BASE\", \"container_image_development\": \"$CONTAINER_IMAGE_DEVELOPMENT\", \"issue_number\": \"$ISSUE_NUMBER\", \"repository\": \"$REPOSITORY\", \"ruleset\": \"$RULESET\", \"sha\": \"$SHA\", \"timeout_in_minutes\": \"$TIMEOUT_IN_MINUTES\"}"
name: trigger-semgrep-comparison-argo
on:
  workflow_call: {}
  workflow_dispatch:
    inputs:
      branch:
        description: The branch to compare against develop
        required: true
        type: string
