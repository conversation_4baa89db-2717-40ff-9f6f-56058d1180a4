# AUTOGENERATED FROM tests.jsonnet DO NOT MODIFY
jobs:
  benchmarks-lite:
    needs:
      - build-test-core-x86
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - name: Fetch semgrep-cli submodules
        run: git submodule update --init --recursive --recommend-shallow cli/src/semgrep/semgrep_interfaces
      - uses: actions/setup-python@v5
        with:
          cache: pipenv
          python-version: "3.9"
      - run: pip install pipenv==2024.0.1
      - uses: actions/download-artifact@v4
        with:
          name: semgrep-core-x86-artifact
      - name: Install artifacts
        run: |
          tar xf artifacts.tgz
          #alt: put it in cli/src/semgrep/bin/, like make copy-core-for-cli
          sudo cp artifacts/* /usr/bin
      - name: Install Python dependencies
        run: pipenv install --dev
        working-directory: cli
      - name: Test dummy benchmarks on latest
        run: |
          pipenv run semgrep --version
          pipenv run semgrep-core -version
          pipenv run python3 ../perf/run-benchmarks --dummy
        working-directory: cli
  build-test-core-x86:
    secrets: inherit
    uses: ./.github/workflows/build-test-core-x86.yml
  build-test-docker:
    secrets: inherit
    uses: ./.github/workflows/build-test-docker.yml
    with:
      artifact-name: semgrep-docker-image-artifact
      docker-flavor: |
        latest=false
      docker-tags: |
        type=ref,event=pr
        type=ref,event=branch
        type=sha,event=branch
      enable-tests: true
      file: Dockerfile
      repository-name: semgrep/semgrep
      target: semgrep-oss
  build-test-docker-performance-tests:
    needs:
      - build-test-docker
    secrets: inherit
    uses: ./.github/workflows/build-test-docker.yml
    with:
      artifact-name: semgrep-docker-image-artifact-performance-tests
      docker-flavor: |
        latest=false
        suffix=-performance-tests
      docker-tags: |
        type=sha,event=branch
        type=ref,event=pr
      enable-tests: false
      file: Dockerfile
      repository-name: semgrep/semgrep
      target: performance-tests
  build-test-manylinux-aarch64:
    needs:
      - build-test-docker
    secrets: inherit
    uses: ./.github/workflows/build-test-manylinux-aarch64.yml
  build-test-manylinux-x86:
    needs:
      - build-test-core-x86
    secrets: inherit
    uses: ./.github/workflows/build-test-manylinux-x86.yml
  build-test-osx-arm64:
    secrets: inherit
    uses: ./.github/workflows/build-test-osx-arm64.yml
  build-test-osx-x86:
    secrets: inherit
    uses: ./.github/workflows/build-test-osx-x86.yml
  build-test-windows-x86:
    secrets: inherit
    uses: ./.github/workflows/build-test-windows-x86.yml
  test-cli:
    name: test semgrep-cli
    needs:
      - build-test-core-x86
    permissions:
      contents: write
      id-token: write
      pull-requests: write
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - name: Fetch semgrep-cli submodules
        run: git submodule update --init --recursive --recommend-shallow cli/src/semgrep/semgrep_interfaces
      - uses: actions/setup-python@v5
        with:
          cache: pipenv
          python-version: ${{ matrix.python }}
      - run: pip install pipenv==2024.0.1
      - name: Install Python dependencies
        run: pipenv install --dev
        working-directory: cli
      - uses: actions/download-artifact@v4
        with:
          name: semgrep-core-x86-artifact
      - name: Install artifacts
        run: |
          tar xf artifacts.tgz
          #alt: put it in cli/src/semgrep/bin/, like make copy-core-for-cli
          sudo cp artifacts/* /usr/bin
      - name: Run pytest
        run: |
          # tests should simulate CI environment iff they need one
          unset CI
          unset "${!GITHUB_@}"

          PYTEST_EXTRA_ARGS="--snapshot-update --allow-snapshot-deletion" make ci-test
        working-directory: cli
      - if: failure()
        name: Prepare repo for snapshot commit
        run: |
          # the commit step that follows will fail to fetch the pfff submodule
          # (perhaps because of the github token's permissions)
          # so we disable recursive fetching
          git config fetch.recurseSubmodules false
      - id: configure-creds
        if: failure() && github.event_name == 'pull_request' && (github.actor != 'dependabot[bot]' && !(github.event.pull_request.head.repo.full_name != github.repository))
        name: Configure git creds for push
        run: |
          echo "machine github.com" >> ~/.netrc
          echo "login ${{ github.repository }}" >> ~/.netrc
          echo "password ${{ secrets.GITHUB_TOKEN }}" >> ~/.netrc
      - id: snapshot-commit
        if: failure() && github.event_name == 'pull_request' && (github.actor != 'dependabot[bot]' && !(github.event.pull_request.head.repo.full_name != github.repository))
        name: Commit snapshot updates
        uses: EndBug/add-and-commit@v9
        with:
          add: cli/tests/default/e2e/snapshots
          default_author: github_actions
          message: Update pytest snapshots
          new_branch: snapshot-updates-${{ github.run_id }}-${{ github.run_attempt }}
      - id: remove-creds
        if: failure() && github.event_name == 'pull_request' && (github.actor != 'dependabot[bot]' && !(github.event.pull_request.head.repo.full_name != github.repository))
        name: Remove Credentials
        run: rm ~/.netrc
      - env:
          COMMIT_LONG_SHA: ${{ steps.snapshot-commit.outputs.commit_long_sha }}
          COMMIT_SHA: ${{ steps.snapshot-commit.outputs.commit_sha }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PULL_REQUEST_NUMBER: ${{ github.event.pull_request.number }}
        if: failure() && steps.snapshot-commit.outputs.pushed == 'true'
        name: Comment about any snapshot updates
        run: |
          echo ":camera_flash: The pytest shapshots changed in your PR." >> /tmp/message.txt
          echo "Please carefully review these changes and make sure they are intended:" >> /tmp/message.txt
          echo >> /tmp/message.txt
          echo "1. Review the changes at https://github.com/semgrep/semgrep/commit/$COMMIT_LONG_SHA" >> /tmp/message.txt
          echo "2. Accept the new snapshots with" >> /tmp/message.txt
          echo >> /tmp/message.txt
          echo "       git fetch origin && git cherry-pick \"$COMMIT_SHA\" && git push" >> /tmp/message.txt

          gh pr comment "$PULL_REQUEST_NUMBER" --body-file /tmp/message.txt
    strategy:
      matrix:
        python:
          - "3.9"
          - "3.10"
          - "3.11"
          - "3.12"
  test-osemgrep:
    container: alpine:3.21
    env:
      HOME: /root
    runs-on: ubuntu-latest
    steps:
      - name: setup alpine
        run: apk add --no-cache git git-lfs bash curl
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - uses: semgrep/setup-ocaml@latest
        with:
          cache-prefix: v1
          ocaml-compiler: 5.3.0
          opam-pin: false
          save-opam-post-run: true
      - name: Build semgrep-core
        run: |
          eval $(opam env)
          make install-deps
          make core
      - name: Install osemgrep
        run: |
          eval $(opam env)
          make copy-core-for-cli
      - name: Install Python dependencies
        run: |
          apk add --no-cache python3 py3-pip
          pip install --no-cache-dir --ignore-installed --break-system-packages distlib pipenv==2024.0.1
          (cd cli; pipenv install --dev)
      - name: Run pytest for osemgrep known passing tests
        run: |
          git config --global --add safe.directory "$(pwd)"
          make osempass
        working-directory: cli
  test-qa:
    name: quality assurance on semgrep
    needs:
      - build-test-core-x86
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - name: Fetch semgrep-cli submodules
        run: git submodule update --init --recursive --recommend-shallow cli/src/semgrep/semgrep_interfaces tests/semgrep-rules
      - uses: actions/setup-python@v5
        with:
          cache: pipenv
          python-version: "3.12"
      - run: pip install pipenv==2024.0.1
      - uses: actions/download-artifact@v4
        with:
          name: semgrep-core-x86-artifact
      - name: Install artifacts
        run: |
          tar xf artifacts.tgz
          #alt: put it in cli/src/semgrep/bin/, like make copy-core-for-cli
          sudo cp artifacts/* /usr/bin
      - name: Install semgrep
        run: |
          export PATH=/github/home/<USER>/bin:$PATH
          pipenv install --dev
        working-directory: cli
      - uses: actions/cache@v4
        with:
          key: qa-public-repos-${{ hashFiles('semgrep/tests/qa/*public_repos*') }}-${{ matrix.split }}
          path: ~/.cache/qa-public-repos
      - run: |
          mkdir -p ~/.cache/qa-public-repos
          touch ~/.cache/qa-public-repos/ok
      - env:
          QA_TESTS_CACHE_PATH: ~/.cache/qa-public-repos
        name: Test semgrep
        run: |
          export PATH=/github/home/<USER>/bin:$PATH
          pipenv run pytest -n auto -vv --tb=short --splits 4 --group ${{ matrix.split }} tests/qa
        working-directory: cli
    strategy:
      fail-fast: false
      matrix:
        split:
          - 1
          - 2
          - 3
          - 4
  test-semgrep-core:
    container: alpine:3.21
    env:
      HOME: /root
    runs-on: ubuntu-latest
    steps:
      - name: setup alpine
        run: apk add --no-cache git git-lfs bash curl
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - uses: semgrep/setup-ocaml@latest
        with:
          cache-prefix: v1
          ocaml-compiler: 5.3.0
          opam-pin: false
          save-opam-post-run: true
      - name: Install dependencies
        run: opam exec -- make install-deps
      - name: Build semgrep-core
        run: opam exec -- make
      - name: Test semgrep-core (and time it)
        run: |
          START=`date +%s`
          opam exec -- make test
          opam exec -- make core-test-e2e

          END=`date +%s`
          TEST_RUN_TIME=$((END-START))
          curl --fail -L -X POST "https://dashboard.semgrep.dev/api/metric/semgrep.core.test-run-time-seconds.num" -d "$TEST_RUN_TIME"
      - if: github.ref == 'refs/heads/develop'
        name: Report Number of Tests Stats
        run: ./scripts/report_test_metrics.sh
name: tests
on:
  pull_request:
    paths-ignore:
      - '**.md'
  push:
    branches:
      - develop
    paths-ignore:
      - '**.md'
  workflow_dispatch: null
permissions:
  contents: write
  id-token: write
