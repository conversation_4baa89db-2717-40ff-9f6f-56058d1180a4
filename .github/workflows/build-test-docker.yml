# AUTOGENERATED FROM build-test-docker.jsonnet DO NOT MODIFY
jobs:
  job:
    permissions:
      contents: read
      id-token: write
    runs-on: ubuntu-latest
    steps:
      - if: ${{ matrix.architecture != 'amd64' }}
        uses: docker/setup-qemu-action@v3
      - uses: docker/setup-buildx-action@v3
      - id: meta
        name: Set tags and labels
        uses: docker/metadata-action@v5
        with:
          flavor: ${{ inputs.docker-flavor }}
          images: ${{ inputs.repository-name }}
          tags: ${{ inputs.docker-tags }}
      - uses: depot/setup-action@v1
      - id: build-image
        name: Build image
        uses: depot/build-push-action@v1.9.0
        with:
          buildx-fallback: false
          file: ${{ inputs.file }}
          labels: ${{ steps.meta.outputs.labels }}
          outputs: type=docker,dest=/tmp/image.tar
          platforms: linux/${{ matrix.architecture }}
          project: fhmxj6w9z8
          secrets: SEMGREP_APP_TOKEN=${{ secrets.SEMGREP_APP_TOKEN }}
          tags: ${{ steps.meta.outputs.tags }}
          target: ${{ inputs.target }}
      - if: ${{ inputs.enable-tests }}
        name: Load image
        run: docker load --input /tmp/image.tar
      - if: ${{ inputs.enable-tests }}
        uses: actions/checkout@v4
      - env:
          IMAGEID: ${{ steps.build-image.outputs.imageid }}
        if: ${{ inputs.enable-tests }}
        name: Test Image
        run: ./scripts/validate-docker-build.sh "$IMAGEID" linux/${{ matrix.architecture }}
      - uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.artifact-name }}-arch-${{ matrix.architecture }}
          path: /tmp/image.tar
    strategy:
      matrix:
        architecture:
          - amd64
          - arm64
name: build-test-docker
on:
  workflow_call:
    inputs:
      artifact-name:
        description: Name (key) to use when uploading the docker image tarball as a artifact
        required: true
        type: string
      docker-flavor:
        description: 'Multi-line string for the metadata tag action for the flavor of the image. '
        required: true
        type: string
      docker-tags:
        description: 'Multi-line string for the metadata tag action for the tags to apply to the image. '
        required: true
        type: string
      enable-tests:
        description: Whether or not to run validation on the built image
        required: true
        type: boolean
      file:
        description: Dockerfile to build
        required: true
        type: string
      repository-name:
        description: The repository/name of the docker image to push, e.g., semgrep/semgrep
        required: true
        type: string
      target:
        description: Dockerfile target to build
        required: true
        type: string
  workflow_dispatch:
    inputs:
      artifact-name:
        description: Name (key) to use when uploading the docker image tarball as a artifact
        required: true
        type: string
      docker-flavor:
        description: 'Multi-line string for the metadata tag action for the flavor of the image. '
        required: true
        type: string
      docker-tags:
        description: 'Multi-line string for the metadata tag action for the tags to apply to the image. '
        required: true
        type: string
      enable-tests:
        description: Whether or not to run validation on the built image
        required: true
        type: boolean
      file:
        default: Dockerfile
        description: Dockerfile to build
        required: true
        type: string
      repository-name:
        default: returntocorp/semgrep
        description: The repository/name of the docker image to push, e.g., semgrep/semgrep
        required: true
        type: string
      target:
        description: Dockerfile target to build
        required: true
        type: string
