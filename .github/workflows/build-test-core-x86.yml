# AUTOGENERATED FROM build-test-core-x86.jsonnet DO NOT MODIFY
jobs:
  job:
    container: alpine:3.21
    env:
      HOME: /root
    runs-on: ubuntu-latest
    steps:
      - name: setup alpine
        run: apk add --no-cache git git-lfs bash curl
      - name: Configure git safedir properly
        run: git config --global --add safe.directory $(pwd)
      - name: Make checkout speedy
        run: git config --global fetch.parallel 50
      - uses: actions/checkout@v4
        with:
          submodules: true
      - uses: semgrep/setup-ocaml@latest
        with:
          cache-prefix: v1
          ocaml-compiler: 5.3.0
          opam-pin: false
          save-opam-post-run: true
      - name: Install dependencies
        run: opam exec -- make install-deps
      - name: Build semgrep-core
        run: opam exec -- make
      - name: Test semgrep-core
        run: opam exec -- make test
      - name: Make artifact for bin/semgrep-core
        run: |
          mkdir artifacts
          cp bin/semgrep-core artifacts/
          tar czf artifacts.tgz artifacts
          # so that we can untar later and not get a message
          # about existing artifacts/ directory
          rm -rf artifacts
      - uses: actions/upload-artifact@v4
        with:
          name: semgrep-core-x86-artifact
          path: artifacts.tgz
name: build-test-core-x86
on:
  workflow_call: null
  workflow_dispatch: null
