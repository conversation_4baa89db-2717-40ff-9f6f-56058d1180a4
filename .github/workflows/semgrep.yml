# AUTOGENERATED FROM semgrep.jsonnet DO NOT MODIFY
jobs:
  semgrep-ci:
    container:
      image: semgrep/semgrep:canary
    env:
      E2E_APP_TOKEN: ${{ secrets.SEMGREP_E2E_APP_TOKEN }}
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
    if: (github.actor != 'dependabot[bot]')
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - run: semgrep ci
  semgrep-ci-debug:
    container:
      image: semgrep/semgrep:canary
    env:
      E2E_APP_TOKEN: ${{ secrets.SEMGREP_E2E_APP_TOKEN }}
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
    if: (github.actor != 'dependabot[bot]')
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - run: semgrep ci --debug
  semgrep-ci-oss:
    container:
      image: semgrep/semgrep:canary
    env:
      E2E_APP_TOKEN: ${{ secrets.SEMGREP_E2E_APP_TOKEN }}
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
    if: (github.actor != 'dependabot[bot]')
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - run: semgrep ci --oss-only
name: semgrep
on:
  pull_request_target: {}
  push:
    branches:
      - develop
  schedule:
    - cron: 50 15 * * *
  workflow_dispatch: null
