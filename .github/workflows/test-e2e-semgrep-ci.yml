# AUTOGENERATED FROM test-e2e-semgrep-ci.jsonnet DO NOT MODIFY
jobs:
  get-inputs:
    name: Get Inputs
    outputs:
      docker_tag: ${{ steps.get-inputs.outputs.docker_tag }}
    runs-on: ubuntu-22.04
    steps:
      - env:
          DOCKER_TAG: develop
          INPUT_DOCKER_TAG: ${{ inputs.docker_tag }}
        id: get-inputs
        name: Set variables
        run: echo "docker_tag=${{ env.INPUT_DOCKER_TAG || env.DOCKER_TAG }}" >> $GITHUB_OUTPUT
      - env:
          DOCKER_TAG: ${{ steps.get-inputs.outputs.docker_tag }}
        name: Debug
        run: echo "$DOCKER_TAG"
  notify-failure:
    if: failure()
    needs:
      - semgrep-ci
      - semgrep-ci-on-pr
      - semgrep-ci-fail-open
      - semgrep-ci-fail-open-blocking-findings
      - wait-for-checks
      - get-inputs
    runs-on: ubuntu-22.04
    steps:
      - run: |
          curl --request POST \
           --url  ${{ secrets.NOTIFICATIONS_URL }} \
           --header 'content-type: application/json' \
           --data "{
             \"text\": \"The End to end semgrep ci workflow failed with docker tag ${{ needs.get-inputs.outputs.docker_tag }}. The PR in `semgrep/e2e` that had the failure was ${{ needs.semgrep-ci-on-pr.outputs.pr-number }}. See https://github.com/semgrep/semgrep/actions/workflows/test-e2e-semgrep-ci.yml for more info\"
           }"
  semgrep-ci:
    container:
      image: semgrep/semgrep-nightly:${{ needs.get-inputs.outputs.docker_tag }}
    env:
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_E2E_APP_TOKEN }}
    needs: get-inputs
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - run: semgrep ci
  semgrep-ci-fail-open:
    container:
      image: semgrep/semgrep-nightly:${{ needs.get-inputs.outputs.docker_tag }}
    env:
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_E2E_APP_TOKEN }}
      SEMGREP_APP_URL: https://staging.semgrep.dev
      SEMGREP_USER_AGENT_APPEND: semgrep-ci-e2e
    needs: get-inputs
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - name: Remove Git Exe
        run: |
          which git
          rm /usr/bin/git
      - run: semgrep ci --suppress-errors
  semgrep-ci-fail-open-blocking-findings:
    container:
      image: semgrep/semgrep-nightly:${{ needs.get-inputs.outputs.docker_tag }}
    env:
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_E2E_APP_TOKEN }}
      SEMGREP_USER_AGENT_APPEND: semgrep-ci-e2e
    needs: get-inputs
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - id: create-code
        name: Create code under test
        run: |
          cat > ./test.py <<- EOF
          import click
          click.echo(click.style("foo"))
          EOF
      - id: run-ci
        name: Run CI
        run: |
          if semgrep ci --suppress-errors; then
             exit 2
          else
             exit 0
          fi
  semgrep-ci-on-pr:
    needs: get-inputs
    outputs:
      pr-number: ${{ steps.open-pr.outputs.pr-number }}
    runs-on: ubuntu-22.04
    steps:
      - env:
          EXPIRATION: 600
          ISSUER: ${{ secrets.SEMGREP_CI_APP_ID }}
          PRIVATE_KEY: ${{ secrets.SEMGREP_CI_APP_KEY }}
        id: jwt
        name: Get JWT for semgrep-ci GitHub App
        uses: docker://public.ecr.aws/y9k7q4m1/devops/cicd:latest
      - env:
          JWT: ${{ steps.jwt.outputs.jwt }}
          SEMGREP_CI_APP_INSTALLATION_ID: ${{ secrets.SEMGREP_CI_APP_INSTALLATION_ID }}
        id: token
        name: Get token for semgrep-ci GitHub App
        run: |
          TOKEN="$(curl -X POST \
          -H "Authorization: Bearer $JWT" \
          -H "Accept: application/vnd.github.v3+json" \
          "https://api.github.com/app/installations/${SEMGREP_CI_APP_INSTALLATION_ID}/access_tokens" | \
          jq -r .token)"
          echo "::add-mask::$TOKEN"
          echo "token=$TOKEN" >> $GITHUB_OUTPUT
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.repository.default_branch }}
          repository: semgrep/e2e
          token: ${{ steps.token.outputs.token }}
      - env:
          DOCKER_TAG: ${{ needs.get-inputs.outputs.docker_tag }}
          RUN_ID: ${{ github.run_id }}
        name: Prepare the PR
        run: |
          git checkout -b "e2e-test-pr-$RUN_ID"
          scripts/change-version.sh "$DOCKER_TAG"
          git config user.name ${{ github.actor }}
          git config user.email ${{ github.actor }}@users.noreply.github.com

          git add --all
          git commit -m "chore: Bump version to $DOCKER_TAG"
          git push --set-upstream origin "e2e-test-pr-$RUN_ID"
      - env:
          DOCKER_TAG: ${{ needs.get-inputs.outputs.docker_tag }}
          GITHUB_TOKEN: ${{ steps.token.outputs.token }}
          RUN_ID: ${{ github.run_id }}
        id: open-pr
        name: Make the PR
        run: |
          PR_URL=$(gh pr create --title "chore: fake PR for $DOCKER_TAG" --body "Fake PR" --base "develop" --head "e2e-test-pr-${RUN_ID}")
          PR_NUMBER=$(echo $PR_URL | sed 's|.*pull/\(.*\)|\1|')
          echo "pr-number=$PR_NUMBER" >> $GITHUB_OUTPUT
  wait-for-checks:
    needs: semgrep-ci-on-pr
    runs-on: ubuntu-22.04
    steps:
      - env:
          EXPIRATION: 600
          ISSUER: ${{ secrets.SEMGREP_CI_APP_ID }}
          PRIVATE_KEY: ${{ secrets.SEMGREP_CI_APP_KEY }}
        id: jwt
        name: Get JWT for semgrep-ci GitHub App
        uses: docker://public.ecr.aws/y9k7q4m1/devops/cicd:latest
      - env:
          JWT: ${{ steps.jwt.outputs.jwt }}
          SEMGREP_CI_APP_INSTALLATION_ID: ${{ secrets.SEMGREP_CI_APP_INSTALLATION_ID }}
        id: token
        name: Get token for semgrep-ci GitHub App
        run: |
          TOKEN="$(curl -X POST \
          -H "Authorization: Bearer $JWT" \
          -H "Accept: application/vnd.github.v3+json" \
          "https://api.github.com/app/installations/${SEMGREP_CI_APP_INSTALLATION_ID}/access_tokens" | \
          jq -r .token)"
          echo "::add-mask::$TOKEN"
          echo "token=$TOKEN" >> $GITHUB_OUTPUT
      - env:
          GITHUB_TOKEN: ${{ steps.token.outputs.token }}
        name: Wait for checks to register
        run: |
          LEN_CHECKS=$(gh pr -R semgrep/e2e view ${{ needs.semgrep-ci-on-pr.outputs.pr-number }} --json statusCheckRollup --jq '.statusCheckRollup | length');
          while [ ${LEN_CHECKS} = "0" ]; do
            echo "No checks available yet"
            sleep 30
            LEN_CHECKS=$(gh pr -R semgrep/e2e view ${{ needs.semgrep-ci-on-pr.outputs.pr-number }} --json statusCheckRollup --jq '.statusCheckRollup | length');
          done
          echo "checks are valid"
          echo ${LEN_CHECKS}
          gh pr -R semgrep/e2e view ${{ needs.semgrep-ci-on-pr.outputs.pr-number }} --json statusCheckRollup
      - env:
          GITHUB_TOKEN: ${{ steps.token.outputs.token }}
        name: Wait for checks to complete
        run: gh pr -R semgrep/e2e checks ${{ needs.semgrep-ci-on-pr.outputs.pr-number }} --interval 30 --watch
name: test-e2e-semgrep-ci
on:
  schedule:
    - cron: 43 20 * * *
  workflow_dispatch:
    inputs:
      docker_tag:
        default: develop
        description: 'Docker Tag to Run. Default: develop (must be in the `semgrep/semgrep-nightly` namespace)'
        required: false
