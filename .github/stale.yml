#
# Stalebot configuration file.
# https://github.com/probot/stale
#
# We use stalebot as follows:
# - it reminds us to prioritize issues that don't have a priority:* label
# - it applies the `stale` label on issues that need prioritizing
#
# This allows us to filter issues by priority.
# Issues aren't automatically closed.
#

# Label to use when marking an issue as stale
staleLabel: stale

# Comment to post when closing a stale issue. Set to `false` to disable
closeComment: <PERSON><PERSON>-bot has closed this stale item. Please reopen it if this is in error.

pulls:
  daysUntilStale: 14
  #  daysUntilClose: 7
  markComment: >-
    This pull request is being marked `stale` because there hasn't
    been any activity in 14 days.

issues:
  daysUntilStale: 14
  #  daysUntilClose: 7
  exemptLabels:
    - tech-debt
    - new-language
    - bug
    - planned-project
    - priority:low
    - priority:medium
  #    - priority:high
  markComment: >-
    This issue is being marked `stale` because there hasn't been any
    activity in 14 days and either it wasn't prioritized or its priority
    is high.
    Please apply the appropriate `priority:*` label before removing the
    `stale` label.
