---
name: Bug report
about: Create a report to help us improve
title: ""
labels: ""
assignees: ""
---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior, ideally a link to https://semgrep.dev/playground (click "share" and make public to get a shortlink):

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**What is the priority of the bug to you?**

- [ ] P0: blocking your adoption of Semgrep or workflow
- [ ] P1: important to fix or quite annoying
- [ ] P2: regular bug that should get fixed

**Environment**
If not using semgrep.dev: are you running off docker, an official binary, a local build?

**Use case**
What will fixing this bug enable for you?
