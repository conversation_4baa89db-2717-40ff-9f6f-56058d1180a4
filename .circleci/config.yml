###############################################################################
# Prelude
###############################################################################
# The main goals of this pipeline are to:
# - dogfood Semgrep by running it on each semgrep PRs.
# - check that Semgrep can run in an alternate CI platform like Circle CI
#   (and not just in Github Actions).
# - have crons that update our benchmarks,
#   which are then accessible at https://dashboard.semgrep.dev/metrics
#   and also at https://metabase.corp.r2c.dev/collection/59-semgrep

version: 2.1

###############################################################################
# The jobs
###############################################################################

jobs:
  # This seems a bit redundant with the .github/workflow/build-xxx workflows
  # but those workflows are using Alpine; here at least we test whether
  # we build under Ubuntu. Moreover, when GHA behaves weird, it's nice to
  # check whether similar commands are working fine in CircleCI.
  build-ubuntu:
    docker:
      - image: returntocorp/ocaml:ubuntu-2023-10-17
        user: root
    working_directory: /src
    steps:
      - checkout
      - run: git submodule update --init
      - run: |
          eval $(opam env)
          make install-deps-for-semgrep-core
          make install-deps
          make core

  # Dogfood!
  semgrep:
    docker:
      - image: returntocorp/semgrep:develop
        user: root
    working_directory: /src
    steps:
      - checkout
      # Dogfooding on the bleeding edge by using jsonnet, the new syntax, osemgrep!
      # coupling: see also 'make check'
      - run: semgrep --experimental --config semgrep.jsonnet --error --strict --verbose --exclude tests

  # For the benchmarks below, we had two alternatives:
  # A. run semgrep container from existing container: requires mounting
  #    a volume, which is disabled by CircleCI (results in empty folder
  #    once mounted).
  # B. run everything inside the semgrep container, which comes with
  #    semgrep and whatever utilities we added.
  #
  # We use B out of necessity.

  # Real benchmarks
  benchmarks:
    docker:
      - image: returntocorp/semgrep:develop
    steps:
      - checkout
      - run:
          name: benchmarks
          command: |
            cd perf
            ./run-benchmarks --upload || true

  # Run autofix parsing stats and publish them to the semgrep dashboard.
  autofix-printing-stats:
    docker:
      # The returntocorp/semgrep:develop image doesn't have OCaml, so
      # instead we use an OCaml image and install the production version of
      # Semgrep.
      #
      # TODO build a docker image with both OCaml and semgrep-core available.
      # Perhaps we can adapt our current Dockerfiles to publish an appropriate
      # image here?
      - image: ocaml/opam:ubuntu
    steps:
      - checkout
      - run:
          name: autofix printing stats
          no_output_timeout: 60m
          command: |
            sudo apt-get update
            sudo apt-get install -y python3-pip jq
            # Pip wants to let apt manage all the packages, but Semgrep isn't
            # available there. Pass --break-system-packages to convince it to
            # install it anyway.
            pip3 install semgrep --break-system-packages
            # Put semgrep-core on the path. This is a little bit shady but
            # apparently the `pip show` output is meant to be machine-parseable
            # so this output should be stable:
            # https://github.com/pypa/pip/issues/5261
            PATH=$PATH:"$(pip3 show semgrep | grep '^Location' | sed -e 's/Location: //')/semgrep/bin"
            opam exec -- ./stats/autofix-printing-stats/run --upload

###############################################################################
# The workflows
###############################################################################

workflows:
  version: 2

  # no triggers, runs on every PRs
  semgrep:
    jobs:
      - semgrep:
          filters:
            branches:
              # we ignore those branches which are used by semgrep-ci
              # because we can't signup semgrep-ci github app in circleCI
              # and get annoying "block-unregister-user" error on those PRs
              ignore:
                - /release-.*/
                - /update-semgrep-rules-.*/

  build-and-check:
    jobs:
      - build-ubuntu:
          filters:
            branches:
              ignore:
                - /release-.*/
                - /update-semgrep-rules-.*/

  #----------------------------------------
  # Crons
  #----------------------------------------

  # Daily semgrep benchmarks
  benchmarks:
    jobs:
      - benchmarks:
          # Run only on these branches
          filters:
            branches:
              only:
                - develop
    triggers:
      - schedule:
          # Run at 00:00 every day, UTC.
          cron: "0 0 * * *"
          filters:
            branches:
              only:
                - develop

  scheduled-autofix-printing-stats:
    jobs:
      - autofix-printing-stats:
          filters:
            branches:
              only:
                - develop
    triggers:
      - schedule:
          cron: "0 0 * * *"
          filters:
            branches:
              only:
                - develop

  scheduled-pysemgrep-loc-stats:
    jobs:
      - pysemgrep-loc-stats:
          filters:
            branches:
              only:
                - develop
    triggers:
      - schedule:
          cron: "0 12 * * *"
          filters:
            branches:
              only:
                - develop
