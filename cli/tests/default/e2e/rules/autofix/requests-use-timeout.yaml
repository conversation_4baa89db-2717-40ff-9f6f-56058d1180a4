rules:
  - id: python.requests.best-practice.use-timeout.use-timeout
    patterns:
      - pattern-not: requests.$W(..., timeout=$N, ...)
      - pattern-not: requests.$W(..., **$KWARGS)
      - pattern-either:
          - pattern: requests.request(...)
          - pattern: requests.get(...)
          - pattern: requests.post(...)
          - pattern: requests.put(...)
          - pattern: requests.delete(...)
          - pattern: requests.head(...)
          - pattern: requests.patch(...)
    fix-regex:
      regex: '(.*)\)'
      replacement: '\1, timeout=30)'
    message: |
      'requests' calls default to waiting until the connection is closed.
      This means a 'requests' call without a timeout will hang the program
      if a response is never received. Consider setting a timeout for all
      'requests'.
    languages: [python]
    severity: WARNING
