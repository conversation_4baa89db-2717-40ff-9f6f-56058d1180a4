rules:
  - id: python.django.security.passwords.use-none-for-password-default.use-none-for-password-default
    message: |
      '$VAR' is using the empty string as its default and is being used to set
      the password on '$MODEL'. If you meant to set an unusable password, set
      the default value to 'None' or call 'set_unusable_password()'.
    metadata:
      cwe: "CWE-521: Weak Password Requirements"
      owasp: "A2: Broken Authentication"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/contrib/auth/#django.contrib.auth.models.User.set_password
    patterns:
      - pattern-either:
          - pattern: |
              $VAR = request.$W.get($X, "")
              ...
              $MODEL.set_password($VAR)
              ...
              $MODEL.save(...)
          - pattern: |
              def $F(..., $VAR="", ...):
                ...
                $MODEL.set_password($VAR)
    fix-regex:
      regex: '(password.*)""'
      replacement: '\1None'
    languages: [python]
    severity: ERROR
