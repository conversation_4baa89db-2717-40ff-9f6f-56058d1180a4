rules:
- id: autofix-bugtest
  languages:
  - python
  message: |
    This rule decreases the file in length and will be applied three times in this test.
  patterns:
    - pattern: $REQUEST_FUNC($...ARGS)
    - pattern-not: $REQUEST_FUNC(..., _injected=$N, ...)
    - metavariable-pattern:
        metavariable: $REQUEST_FUNC
        patterns:
          - pattern: requests.post
  fix: $REQUEST_FUNC($...ARGS, _injected=True)
  severity: ERROR
