rules:
  - id: first-rule
    pattern: |
      a = $FOO
      ...
      c = $BAR
    fix: |
      once
    message: <PERSON>mg<PERSON>p found a match
    languages:
      - python
    severity: WARNING
  - id: second-rule
    pattern: |
      b = $FOO
      ...
      d = $XD
    fix: |
      twice
    message: <PERSON><PERSON>g<PERSON><PERSON> found a match
    languages:
      - python
    severity: WARNING
