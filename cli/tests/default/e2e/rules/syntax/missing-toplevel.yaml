# top level key should be "rules"
rule:
  - id: flask-secure-set-cookie
    languages: [python]
    patterns:
      - pattern-not: |
          flask.response.set_cookie(..., httponly=True, secure=True,...)
      - pattern: |
          flask.response.set_cookie(....)
    message: |
      Flask cookies should be handled securely by setting secure=True, httponly=True, and samesite='Lax' in  response.set_cookie(...). If your situation calls for different settings, explicitly disable the setting.
      If you want to send the cookie over http, set secure=False.  If you want to let client-side JavaScript
      read the cookie, set httponly=False. If you want to attach cookies to requests for external sites,
      set samesite=None.
    severity: error
