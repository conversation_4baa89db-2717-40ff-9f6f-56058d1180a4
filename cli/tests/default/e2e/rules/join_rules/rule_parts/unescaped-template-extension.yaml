rules:
  - id: python.flask.security.unescaped-template-extension.unescaped-template-extension
    message: |
      <PERSON><PERSON><PERSON> does not automatically escape Jinja templates unless they have
      .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks.
      Use .html, .htm, .xml, or .xhtml for your template extensions.
      See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup
      for more information.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      source-rule-url: https://pypi.org/project/flake8-flask/
      references:
        - https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup
        - https://blog.r2c.dev/2020/bento-check-unescaped-template-extensions-in-flask/
        - https://bento.dev/checks/flask/unescaped-file-extension/
      category: security
      technology:
        - flask
      source: https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension
    patterns:
      - pattern: flask.render_template("$PATH", ..., $VAR=$VALUE, ...)
      - metavariable-regex:
          metavariable: "$PATH"
          regex: ".*(?<!html)$"
    languages:
      - python
    severity: WARNING
