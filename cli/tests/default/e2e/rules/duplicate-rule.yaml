rules:
  - id: duplicate-eq
    pattern: $X == $X
    message: Found redundant comparison
    languages: [py]
    severity: WARNING
    metadata:
      cwe: "CWE-522: Insufficiently Protected Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.5.2 Static API keys or secret
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management
        version: "4"
      category: security
      technology:
        - jwt
      license: Commons Clause License Condition v1.0[LGPL-2.1-only]
      source: https://semgrep.dev/r/javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret
      shortlink: https://sg.run/4xN9
      semgrep.policy:
        id: 18613
        name: Rule Board - Block column
        slug: rule-board-block
      semgrep.url: https://semgrep.dev/r/javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret
      semgrep.ruleset: ci
      semgrep.ruleset_id: 735
  - id: duplicate-eq
    pattern: $X == $X
    message: Found redundant comparison
    languages: [py]
    severity: WARNING
    metadata:
      cwe: "CWE-522: Insufficiently Protected Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.5.2 Static API keys or secret
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management
        version: "4"
      category: security
      technology:
        - jwt
      license: Commons Clause License Condition v1.0[LGPL-2.1-only]
      source: https://semgrep.dev/r/javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret
      shortlink: https://sg.run/4xN9
      semgrep.policy:
        id: 18613
        name: Rule Board - Block column
        slug: rule-board-block
      semgrep.url: https://semgrep.dev/r/javascript.jsonwebtoken.security.jwt-hardcode.hardcoded-jwt-secret
      semgrep.ruleset: other
      semgrep.ruleset_id: 1234
