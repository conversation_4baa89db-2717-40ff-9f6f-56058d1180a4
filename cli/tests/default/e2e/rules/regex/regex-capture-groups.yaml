rules:
  - id: regex-capture-groups
    message: <PERSON><PERSON><PERSON><PERSON><PERSON> found a match
    languages:
      - python
    severity: WARNING
    pattern-either:
      - patterns:
          - pattern-regex: "foo\\((?P<X>.*)\\)"
          - metavariable-regex:
              metavariable: $X
              regex: "150"
          - metavariable-comparison:
              metavariable: $X
              comparison: $X > 122
      - patterns:
          - pattern-regex: "bar\\((?P<A>.*),(?P<B>.*)\\)"
          - metavariable-regex:
              metavariable: $A
              regex: "a"
          - focus-metavariable: $B
