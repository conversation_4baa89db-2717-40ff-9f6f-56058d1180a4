rules:
  - id: detected-aws-account-id
    pattern-regex: |-
      ("|')?(?P<AWS>AWS|aws|Aws)?_?(?P<ACCOUNT>ACCOUNT|account|Account)_?(?P<ID>ID|id|Id)?("|')?\s*(?P<DELIM>:|=>|=)\s*("|')?[0-9]{12}("|')?
    languages: [regex]
    message: AWS Account ID detected
    severity: ERROR
    metadata:
      source-rule-url: https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go
      shortlink: https://sg.run/xyz2
