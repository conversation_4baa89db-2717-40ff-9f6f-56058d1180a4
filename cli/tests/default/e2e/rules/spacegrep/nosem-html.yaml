rules:
  - id: var-in-href
    message: >-
      Detected a template variable used in an anchor tag with
      the 'href' attribute. This allows a malicious actor to
      input the 'javascript:' URI and is subject to cross-
      site scripting (XSS) attacks.
      If using Flask, use 'url_for()' to safely generate a URL.
      If using Django, use the 'url' filter to safely generate a URL.
      If using Mustache, use a URL encoding library, or prepend a slash '/' to the
      variable for relative links (`href="/{{link}}"`).
      You may also consider setting the Content Security Policy (CSP) header.
    metadata:
      cwe: "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"
      owasp: "A7: Cross-site Scripting (XSS)"
      references:
        - https://flask.palletsprojects.com/en/1.1.x/security/#cross-site-scripting-xss#:~:text=javascript:%20URI
        - https://docs.djangoproject.com/en/3.1/ref/templates/builtins/#url
        - https://github.com/pugjs/pug/issues/2952
        - https://content-security-policy.com/
    languages:
      - generic
    paths:
      include:
        - "*.html"
        - "*.mustache"
        - "*.hbs"
    severity: WARNING
    patterns:
      - pattern-inside: <a ...>
      - pattern-either:
          - pattern: href = {{ ... }}
          - pattern: href = "{{ ... }}"
          - pattern: href = '{{ ... }}'
      # OK for Flask
      - pattern-not-inside: href = {{ url_for(...) ... }}
      - pattern-not-inside: href = "{{ url_for(...) ... }}"
      - pattern-not-inside: href = '{{ url_for(...) ... }}'
      # OK for all templates
      - pattern-not-inside: href = "/{{ ... }}"
      - pattern-not-inside: href = '/{{ ... }}'
