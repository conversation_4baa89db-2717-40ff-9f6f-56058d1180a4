rules:
  - id: assign-multiline-comment
    message: found an assignment of a multiline comment
    severity: WARNING
    languages: [python]
    pattern: $X = """$MULTILINE"""
  - id: eqeq-is-bad
    patterns:
      - pattern-not-inside: |
          def __eq__(...):
              ...
      - pattern-not-inside: assert(...)
      - pattern-not-inside: assertTrue(...)
      - pattern-not-inside: assertFalse(...)
      - pattern-either:
          - pattern: $X == $X
          - pattern: $X != $X
          - patterns:
              - pattern-inside: |
                  def __init__(...):
                      ...
              - pattern: self.$X == self.$X
      - pattern-not: 1 == 1
    message: "useless comparison operation `$X == $X` or `$X != $X`; possible bug?"
    languages: [python]
    severity: ERROR
    metadata:
      shortlink: https://sg.run/xyz1
