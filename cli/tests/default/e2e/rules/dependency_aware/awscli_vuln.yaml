rules:
  - id: vulnerable-awscli-apr-2017
    patterns:
      - pattern-either:
          - pattern: boto3.resource('s3', ...)
          - pattern: boto3.client('s3', ...)
    r2c-internal-project-depends-on:
        namespace: pypi
        package: awscli
        version: "== 1.11.82"
    message: this version of awscli is subject to a directory traversal vulnerability in the s3 module 1
    languages: [python]
    severity: WARNING
  - id: unconditional-depends-on-only
    r2c-internal-project-depends-on:
        namespace: pypi
        package: awscli
        version: "== 1.11.82"
    message: this version of awscli is subject to a directory traversal vulnerability in the s3 module 2
    languages: [python]
    severity: WARNING
  - id: vulnerable-awscli-apr-2017-wrong-pattern
    pattern: neverexists.resource('s3', ...)
    r2c-internal-project-depends-on:
        namespace: pypi
        package: awscli
        version: "== 1.11.82"
    message: this version of awsc<PERSON> is subject to a directory traversal vulnerability in the s3 module 1
    languages: [python]
    severity: WARNING
  - id: wrong-version
    pattern-either:
      - pattern: boto3.resource('s3', ...)
      - pattern: boto3.client('s3', ...)
    r2c-internal-project-depends-on:
        namespace: pypi
        package: awscli
        version: "== 1.11.83"
    message: this version of awscli is subject to a directory traversal vulnerability in the s3 module 3
    languages: [python]
    severity: WARNING
  - id: version-ge
    pattern-either:
      - pattern: boto3.resource('s3', ...)
      - pattern: boto3.client('s3', ...)
    r2c-internal-project-depends-on:
        namespace: pypi
        package: awscli
        version: ">= 0.0.1"
    message: this version of awscli is subject to a directory traversal vulnerability in the s3 module 3
    languages: [python]
    severity: WARNING
  - id: version-leq
    pattern-either:
      - pattern: boto3.resource('s3', ...)
      - pattern: boto3.client('s3', ...)
    r2c-internal-project-depends-on:
        namespace: pypi
        package: awscli
        version: "<= 1.11.82"
    message: this version of awscli is subject to a directory traversal vulnerability in the s3 module 1
    languages: [python]
    severity: WARNING
