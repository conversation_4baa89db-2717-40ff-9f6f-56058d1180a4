{"errors": [], "paths": {"scanned": ["targets/autofix/csv-writer.py"]}, "results": [{"check_id": "rules.autofix.python.lang.security.unquoted-csv-writer.unquoted-csv-writer", "end": {"col": 50, "line": 9, "offset": 151}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "csv.writer(csvfile, delimiter=',', quotechar='\"', quoting=csv.QUOTE_ALL)", "fixed_lines": ["csv.writer(csvfile, delimiter=',', quotechar='\"', quoting=csv.QUOTE_ALL)"], "lines": "requires login", "message": "Found an unquoted CSV writer. This is susceptible to injection. Use 'quoting=csv.QUOTE_ALL'.", "metadata": {"cwe": "CWE-1236: Improper Neutralization of Formula Elements in a CSV File", "owasp": "A1: Injection", "references": ["https://affinity-it-security.com/how-to-prevent-csv-injection/"]}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/csv-writer.py", "start": {"col": 1, "line": 9, "offset": 102}}, {"check_id": "rules.autofix.python.lang.security.unquoted-csv-writer.unquoted-csv-writer", "end": {"col": 53, "line": 12, "offset": 234}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "csv.writer(get_file(), delimiter=',', quotechar='\"', quoting=csv.QUOTE_ALL)", "fixed_lines": ["csv.writer(get_file(), delimiter=',', quotechar='\"', quoting=csv.QUOTE_ALL)"], "lines": "requires login", "message": "Found an unquoted CSV writer. This is susceptible to injection. Use 'quoting=csv.QUOTE_ALL'.", "metadata": {"cwe": "CWE-1236: Improper Neutralization of Formula Elements in a CSV File", "owasp": "A1: Injection", "references": ["https://affinity-it-security.com/how-to-prevent-csv-injection/"]}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/csv-writer.py", "start": {"col": 1, "line": 12, "offset": 182}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}