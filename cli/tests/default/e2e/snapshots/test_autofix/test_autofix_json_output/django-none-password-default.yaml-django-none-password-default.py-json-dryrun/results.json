{"errors": [], "paths": {"scanned": ["targets/autofix/django-none-password-default.py"]}, "results": [{"check_id": "rules.autofix.python.django.security.passwords.use-none-for-password-default.use-none-for-password-default", "end": {"col": 20, "line": 63, "offset": 2427}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "new_password = request.data.get(\"password\", None)\n        validate_password(new_password, user=user)\n        user.set_password(new_password)\n        user.save()", "fixed_lines": ["        new_password = request.data.get(\"password\", None)", "        validate_password(new_password, user=user)", "        user.set_password(new_password)", "        user.save()"], "lines": "requires login", "message": "'new_password' is using the empty string as its default and is being used to set\nthe password on 'user'. If you meant to set an unusable password, set\nthe default value to 'None' or call 'set_unusable_password()'.\n", "metadata": {"cwe": "CWE-521: Weak Password Requirements", "owasp": "A2: Broken Authentication", "references": ["https://docs.djangoproject.com/en/3.0/ref/contrib/auth/#django.contrib.auth.models.User.set_password"]}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/django-none-password-default.py", "start": {"col": 9, "line": 60, "offset": 2269}}, {"check_id": "rules.autofix.python.django.security.passwords.use-none-for-password-default.use-none-for-password-default", "end": {"col": 20, "line": 81, "offset": 3058}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "def create_user(self, email, password=None):\n        \"\"\"\n        Creates and saves a Poster with the given email and password.\n        \"\"\"\n        if not email:\n            raise ValueError('Users must have an email address')\n\n        user = self.model(email=self.normalize_email(email))\n        user.set_password(password)\n        user.save(using=self._db)\n        return user", "fixed_lines": ["    def create_user(self, email, password=None):", "        \"\"\"", "        Creates and saves a Poster with the given email and password.", "        \"\"\"", "        if not email:", "            raise ValueError('Users must have an email address')", "", "        user = self.model(email=self.normalize_email(email))", "        user.set_password(password)", "        user.save(using=self._db)", "        return user"], "lines": "requires login", "message": "'password' is using the empty string as its default and is being used to set\nthe password on 'user'. If you meant to set an unusable password, set\nthe default value to 'None' or call 'set_unusable_password()'.\n", "metadata": {"cwe": "CWE-521: Weak Password Requirements", "owasp": "A2: Broken Authentication", "references": ["https://docs.djangoproject.com/en/3.0/ref/contrib/auth/#django.contrib.auth.models.User.set_password"]}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/django-none-password-default.py", "start": {"col": 5, "line": 71, "offset": 2683}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}