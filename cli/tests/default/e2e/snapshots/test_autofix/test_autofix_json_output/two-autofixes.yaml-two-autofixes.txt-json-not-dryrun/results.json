{"errors": [], "paths": {"scanned": ["targets/autofix/two-autofixes.txt"]}, "results": [{"check_id": "rules.autofix.remove-two", "end": {"col": 4, "line": 2, "offset": 7}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "one", "lines": "requires login", "message": "This rule changes the line numbers for the other rule's match", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/two-autofixes.txt", "start": {"col": 1, "line": 1, "offset": 0}}, {"check_id": "rules.autofix.remove-four", "end": {"col": 5, "line": 4, "offset": 18}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "four", "lines": "requires login", "message": "If semgrep is not smart enough, the match of this rule will be out of range", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/two-autofixes.txt", "start": {"col": 1, "line": 3, "offset": 8}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}