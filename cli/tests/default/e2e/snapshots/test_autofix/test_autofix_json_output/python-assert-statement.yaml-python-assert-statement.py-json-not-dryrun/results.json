{"errors": [], "paths": {"scanned": ["targets/autofix/python-assert-statement.py"]}, "results": [{"check_id": "rules.autofix.assert_eq-true", "end": {"col": 2, "line": 3, "offset": 26}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "assert \"a\"", "lines": "requires login", "message": "Change assert_eq(True, x) to assert x", "metadata": {}, "severity": "INFO", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-assert-statement.py", "start": {"col": 1, "line": 1, "offset": 0}}, {"check_id": "rules.autofix.assert_eq-true", "end": {"col": 2, "line": 6, "offset": 53}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "assert \"b\"", "lines": "requires login", "message": "Change assert_eq(True, x) to assert x", "metadata": {}, "severity": "INFO", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-assert-statement.py", "start": {"col": 1, "line": 4, "offset": 27}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}