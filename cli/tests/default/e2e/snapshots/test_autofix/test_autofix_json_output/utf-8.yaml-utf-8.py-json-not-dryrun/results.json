{"errors": [], "paths": {"scanned": ["targets/autofix/utf-8.py"]}, "results": [{"check_id": "rules.autofix.replace-with-smiley", "end": {"col": 11, "line": 12, "offset": 442}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "print('😊')", "lines": "requires login", "message": "Unnecessary parameter which matches the default", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/utf-8.py", "start": {"col": 1, "line": 12, "offset": 432}}, {"check_id": "rules.autofix.replace-with-smiley", "end": {"col": 23, "line": 16, "offset": 595}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "print('😊')", "lines": "requires login", "message": "Unnecessary parameter which matches the default", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/utf-8.py", "start": {"col": 13, "line": 16, "offset": 585}}, {"check_id": "rules.autofix.replace-with-smiley", "end": {"col": 35, "line": 16, "offset": 607}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "print('😊')", "lines": "requires login", "message": "Unnecessary parameter which matches the default", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/utf-8.py", "start": {"col": 25, "line": 16, "offset": 597}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}