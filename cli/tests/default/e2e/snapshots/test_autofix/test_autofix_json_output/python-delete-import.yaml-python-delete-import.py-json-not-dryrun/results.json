{"errors": [], "paths": {"scanned": ["targets/autofix/python-delete-import.py"]}, "results": [{"check_id": "rules.autofix.python-typing-osemgrep", "end": {"col": 24, "line": 2, "offset": 34}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "", "lines": "requires login", "message": "found one", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-delete-import.py", "start": {"col": 1, "line": 2, "offset": 11}}, {"check_id": "rules.autofix.python-typing-osemgrep", "end": {"col": 24, "line": 3, "offset": 58}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "", "lines": "requires login", "message": "found one", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-delete-import.py", "start": {"col": 1, "line": 3, "offset": 35}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}