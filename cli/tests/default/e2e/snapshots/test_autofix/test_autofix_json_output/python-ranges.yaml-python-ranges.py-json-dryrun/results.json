{"errors": [], "paths": {"scanned": ["targets/autofix/python-ranges.py"]}, "results": [{"check_id": "rules.autofix.range-test", "end": {"col": 11, "line": 1, "offset": 10}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar('abc')", "fixed_lines": ["bar('abc')"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 1, "offset": 0}}, {"check_id": "rules.autofix.range-test", "end": {"col": 11, "line": 2, "offset": 21}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar(\"abc\")", "fixed_lines": ["bar(\"abc\")"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 2, "offset": 11}}, {"check_id": "rules.autofix.range-test", "end": {"col": 15, "line": 3, "offset": 36}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar(\"\"\"abc\"\"\")", "fixed_lines": ["bar(\"\"\"abc\"\"\")"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 3, "offset": 22}}, {"check_id": "rules.autofix.range-test", "end": {"col": 12, "line": 4, "offset": 48}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar(r\"abc\")", "fixed_lines": ["bar(r\"abc\")"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 4, "offset": 37}}, {"check_id": "rules.autofix.range-test", "end": {"col": 12, "line": 5, "offset": 60}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar(f\"abc\")", "fixed_lines": ["bar(f\"abc\")"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 5, "offset": 49}}, {"check_id": "rules.autofix.range-test", "end": {"col": 9, "line": 6, "offset": 69}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar([1])", "fixed_lines": ["bar([1])"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 6, "offset": 61}}, {"check_id": "rules.autofix.range-test", "end": {"col": 12, "line": 7, "offset": 81}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar([1, 2])", "fixed_lines": ["bar([1, 2])"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 7, "offset": 70}}, {"check_id": "rules.autofix.range-test", "end": {"col": 10, "line": 8, "offset": 91}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar(x[1])", "fixed_lines": ["bar(x[1])"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 8, "offset": 82}}, {"check_id": "rules.autofix.range-test", "end": {"col": 13, "line": 9, "offset": 104}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "bar(x[1, 2])", "fixed_lines": ["bar(x[1, 2])"], "lines": "requires login", "message": "change it", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/python-ranges.py", "start": {"col": 1, "line": 9, "offset": 92}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}