{"errors": [], "paths": {"scanned": ["targets/autofix/three-autofixes.py"]}, "results": [{"check_id": "rules.autofix.autofix-bugtest", "end": {"col": 6, "line": 8, "offset": 147}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "requests.post(headers=auth_headers, url=self.url, endpoints=\"endpoint\", _injected=True)", "fixed_lines": ["    return requests.post(headers=auth_headers, url=self.url, endpoints=\"endpoint\", _injected=True)"], "lines": "requires login", "message": "This rule decreases the file in length and will be applied three times in this test.\n", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/three-autofixes.py", "start": {"col": 12, "line": 4, "offset": 45}}, {"check_id": "rules.autofix.autofix-bugtest", "end": {"col": 6, "line": 16, "offset": 286}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "requests.post(headers=auth_headers, url=self.url, endpoints=\"endpoint\", _injected=True)", "fixed_lines": ["    return requests.post(headers=auth_headers, url=self.url, endpoints=\"endpoint\", _injected=True)"], "lines": "requires login", "message": "This rule decreases the file in length and will be applied three times in this test.\n", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/three-autofixes.py", "start": {"col": 12, "line": 12, "offset": 184}}, {"check_id": "rules.autofix.autofix-bugtest", "end": {"col": 6, "line": 24, "offset": 425}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "requests.post(headers=auth_headers, url=self.url, endpoints=\"endpoint\", _injected=True)", "fixed_lines": ["    return requests.post(headers=auth_headers, url=self.url, endpoints=\"endpoint\", _injected=True)"], "lines": "requires login", "message": "This rule decreases the file in length and will be applied three times in this test.\n", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/three-autofixes.py", "start": {"col": 12, "line": 20, "offset": 324}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}