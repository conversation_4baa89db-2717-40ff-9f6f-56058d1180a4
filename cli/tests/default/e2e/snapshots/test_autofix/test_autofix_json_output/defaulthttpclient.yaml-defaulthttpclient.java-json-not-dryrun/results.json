{"errors": [], "paths": {"scanned": ["targets/autofix/defaulthttpclient.java"]}, "results": [{"check_id": "rules.autofix.java.lang.security.audit.crypto.ssl.defaulthttpclient-is-deprecated.defaulthttpclient-is-deprecated", "end": {"col": 47, "line": 15, "offset": 499}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "HttpClient client = new SystemDefaultHttpClient();", "lines": "requires login", "message": "DefaultHttpClient is deprecated. Further, it does not support connections\nusing TLS1.2, which makes using DefaultHttpClient a security hazard.\nUse SystemDefaultHttpClient instead, which supports TLS1.2.\n", "metadata": {"cwe": "CWE-326: Inadequate Encryption Strength", "owasp": "A3: Sensitive Data Exposure", "source-rule-url": "https://find-sec-bugs.github.io/bugs.htm#DEFAULT_HTTP_CLIENT"}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/defaulthttpclient.java", "start": {"col": 3, "line": 15, "offset": 455}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}