{"errors": [], "paths": {"scanned": ["targets/autofix/imported-entity.py"]}, "results": [{"check_id": "rules.autofix.exception-useless-parentheses", "end": {"col": 22, "line": 4, "offset": 80}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "raise RandomError", "lines": "requires login", "message": "Found useless parentheses", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/imported-entity.py", "start": {"col": 3, "line": 4, "offset": 61}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}