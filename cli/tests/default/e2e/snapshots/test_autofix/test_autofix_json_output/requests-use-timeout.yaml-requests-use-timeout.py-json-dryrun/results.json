{"errors": [], "paths": {"scanned": ["targets/autofix/requests-use-timeout.py"]}, "results": [{"check_id": "rules.autofix.python.requests.best-practice.use-timeout.use-timeout", "end": {"col": 22, "line": 6, "offset": 84}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "requests.get(url, timeout=30)", "fixed_lines": ["r = requests.get(url, timeout=30)"], "lines": "requires login", "message": "'requests' calls default to waiting until the connection is closed.\nThis means a 'requests' call without a timeout will hang the program\nif a response is never received. Consider setting a timeout for all\n'requests'.\n", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/requests-use-timeout.py", "start": {"col": 5, "line": 6, "offset": 67}}, {"check_id": "rules.autofix.python.requests.best-practice.use-timeout.use-timeout", "end": {"col": 23, "line": 9, "offset": 130}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "requests.post(url, timeout=30)", "fixed_lines": ["r = requests.post(url, timeout=30)"], "lines": "requires login", "message": "'requests' calls default to waiting until the connection is closed.\nThis means a 'requests' call without a timeout will hang the program\nif a response is never received. Consider setting a timeout for all\n'requests'.\n", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/requests-use-timeout.py", "start": {"col": 5, "line": 9, "offset": 112}}, {"check_id": "rules.autofix.python.requests.best-practice.use-timeout.use-timeout", "end": {"col": 33, "line": 12, "offset": 186}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "requests.request(\"GET\", url, timeout=30)", "fixed_lines": ["r = requests.request(\"GET\", url, timeout=30)"], "lines": "requires login", "message": "'requests' calls default to waiting until the connection is closed.\nThis means a 'requests' call without a timeout will hang the program\nif a response is never received. Consider setting a timeout for all\n'requests'.\n", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/requests-use-timeout.py", "start": {"col": 5, "line": 12, "offset": 158}}, {"check_id": "rules.autofix.python.requests.best-practice.use-timeout.use-timeout", "end": {"col": 42, "line": 18, "offset": 285}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "requests.request(\"GET\", return_url(), timeout=30)", "fixed_lines": ["r = requests.request(\"GET\", return_url(), timeout=30)"], "lines": "requires login", "message": "'requests' calls default to waiting until the connection is closed.\nThis means a 'requests' call without a timeout will hang the program\nif a response is never received. Consider setting a timeout for all\n'requests'.\n", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/requests-use-timeout.py", "start": {"col": 5, "line": 18, "offset": 248}}, {"check_id": "rules.autofix.python.requests.best-practice.use-timeout.use-timeout", "end": {"col": 18, "line": 29, "offset": 471}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "post(url, timeout=30)", "fixed_lines": ["    r = post(url, timeout=30)"], "lines": "requires login", "message": "'requests' calls default to waiting until the connection is closed.\nThis means a 'requests' call without a timeout will hang the program\nif a response is never received. Consider setting a timeout for all\n'requests'.\n", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/requests-use-timeout.py", "start": {"col": 9, "line": 29, "offset": 462}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}