{"errors": [], "paths": {"scanned": ["targets/autofix/flask-use-jsonify.py"]}, "results": [{"check_id": "rules.autofix.python.flask.best-practice.use-jsonify.use-jsonify", "end": {"col": 33, "line": 10, "offset": 209}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "flask.jsonify(user_dict)", "fixed_lines": ["    return flask.jsonify(user_dict)"], "lines": "requires login", "message": "flask.jsonify() is a Flask helper method which handles the correct settings for returning JSON from Flask routes", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/flask-use-jsonify.py", "start": {"col": 12, "line": 10, "offset": 188}}, {"check_id": "rules.autofix.python.flask.best-practice.use-jsonify.use-jsonify", "end": {"col": 28, "line": 18, "offset": 368}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "fix": "flask.jsonify(user_dict)", "fixed_lines": ["    return flask.jsonify(user_dict)"], "lines": "requires login", "message": "flask.jsonify() is a Flask helper method which handles the correct settings for returning JSON from Flask routes", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/autofix/flask-use-jsonify.py", "start": {"col": 12, "line": 18, "offset": 352}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}