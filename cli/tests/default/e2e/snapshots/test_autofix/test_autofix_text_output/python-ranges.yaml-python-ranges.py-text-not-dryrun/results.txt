

┌─────────────────┐
│ 9 Code Findings │
└─────────────────┘

    targets/autofix/python-ranges.py
   ❯❯❱ rules.autofix.range-test
          change it

           ▶▶┆ Autofix ▶ bar('abc')
            1┆ foo('abc')
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ bar("abc")
            2┆ foo("abc")
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ bar("""abc""")
            3┆ foo("""abc""")
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ bar(r"abc")
            4┆ foo(r"abc")
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ bar(f"abc")
            5┆ foo(f"abc")
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ bar([1])
            6┆ foo([1])
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ bar([1, 2])
            7┆ foo([1, 2])
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ bar(x[1])
            8┆ foo(x[1])
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ bar(x[1, 2])
            9┆ foo(x[1, 2])

