

┌────────────────┐
│ 1 Code Finding │
└────────────────┘

    targets/autofix/defaulthttpclient.java
    ❯❱ rules.autofix.java.lang.security.audit.crypto.ssl.defaulthttpclient-is-deprecated.defaulthttpclient-is-
       deprecated
          DefaultHttpClient is deprecated. Further, it does not support connections using TLS1.2, which makes
          using DefaultHttpClient a security hazard. Use SystemDefaultHttpClient instead, which supports
          TLS1.2.

           ▶▶┆ Autofix ▶ HttpClient client = new SystemDefaultHttpClient();
           15┆ HttpClient client = new DefaultHttpClient();

