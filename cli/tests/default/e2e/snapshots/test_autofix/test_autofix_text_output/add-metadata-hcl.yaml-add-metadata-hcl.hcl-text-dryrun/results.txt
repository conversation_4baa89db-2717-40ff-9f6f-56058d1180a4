

┌─────────────────┐
│ 2 Code Findings │
└─────────────────┘

    targets/autofix/add-metadata-hcl.hcl
    ❯❱ rules.autofix.ec2-instance-metadata-options
          EC2 instance does not set metadata options

           ▶▶┆ Autofix ▶ resource "aws_instance" "example1" { ami           = "ami-005e54dee72cc1d01" instance_type =
              "t2.micro"  metadata_options { http_tokens = "required" } }
            1┆ resource "aws_instance" "example1" {  ami           = "ami-005e54dee72cc1d01"
               instance_type = "t2.micro"  metadata_options {    http_tokens = "required"  }}
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ resource "aws_instance" "example2" { ami           = "ami-005e54dee72cc1d02" instance_type =
              "t2.micro"  metadata_options { http_tokens = "required" } }
            6┆ resource "aws_instance" "example2" {  ami           = "ami-005e54dee72cc1d02"
               instance_type = "t2.micro"  metadata_options {    http_tokens = "required"  }}

