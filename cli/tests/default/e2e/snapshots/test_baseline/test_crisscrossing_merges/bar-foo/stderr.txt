

┌─────────────┐
│ Scan Status │
└─────────────┘
  Scanning 1 file tracked by git with 1 Code rule:
  Scanning 1 file.
  Current version has 9 findings.

Creating git worktree from 'foo' to scan baseline.
  Will report findings introduced by these commits (may be incomplete for shallow checkouts):
    * ea6d96d commit #9
    * 0367792 commit #8
    * 4a37202 commit #7
    * 5fe55c3 commit #6
    * f0d065e commit #5
    * b62d8d6 commit #4
  The current branch is missing these commits from the baseline branch:
    * fe51afb merging bar~6
    * 4917fac commit #9
    * 9914f12 commit #8
    * c9ac2d0 commit #7
    * 01aedc2 commit #6
    * 657cdba commit #5
    * 4c7d0ba commit #4
    * 5275d4b commit #3
    * 9121d4d commit #2
  Any finding these commits fixed will look like a new finding in the current branch.
  To avoid reporting such findings, compare to the branch-off point with:
    --baseline-commit=$(git merge-base foo HEAD)



┌─────────────┐
│ Scan Status │
└─────────────┘
  Scanning 1 file tracked by git with 1 Code rule:
  Scanning 1 file.


┌──────────────┐
│ Scan Summary │
└──────────────┘
✅ Scan completed successfully.
 • Findings: 6 (6 blocking)
 • Rules run: 1
 • Targets scanned: 1
 • Parsed lines: ~100.0%
 • <PERSON>an was limited to files changed since baseline commit.
 • For a detailed list of skipped files and lines, run semgrep with the --verbose flag
Ran 1 rule on 1 file: 6 findings.
