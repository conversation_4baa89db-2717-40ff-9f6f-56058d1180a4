

┌─────────────┐
│ Scan Status │
└─────────────┘
  Scanning 1 file tracked by git with 1 Code rule:
  Scanning 1 file.
  Current version has 2 findings.

Creating git worktree from 'baseline-commit' to scan baseline.
  Will report findings introduced by these commits (may be incomplete for shallow checkouts):
    * ac0f5a0 commit #2



┌─────────────┐
│ Scan Status │
└─────────────┘
  Scanning 1 file tracked by git with 1 Code rule:
  Scanning 1 file.


┌──────────────┐
│ Scan Summary │
└──────────────┘
✅ Scan completed successfully.
 • Findings: 1 (1 blocking)
 • Rules run: 1
 • Targets scanned: 1
 • Parsed lines: ~100.0%
 • Scan was limited to files changed since baseline commit.
 • For a detailed list of skipped files and lines, run semgrep with the --verbose flag
Ran 1 rule on 1 file: 1 finding.
