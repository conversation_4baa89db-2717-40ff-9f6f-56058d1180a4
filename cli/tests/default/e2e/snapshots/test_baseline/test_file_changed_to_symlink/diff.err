

┌─────────────┐
│ Scan Status │
└─────────────┘
  Scanning 1 file tracked by git with 1 Code rule:
  Scanning 1 file.
  Current version has 1 finding.

Skipping baseline scan, because all current findings are in files that didn't exist in the baseline commit.


┌──────────────┐
│ Scan Summary │
└──────────────┘
✅ <PERSON>an completed successfully.
 • Findings: 1 (1 blocking)
 • Rules run: 1
 • Targets scanned: 1
 • Parsed lines: ~100.0%
 • <PERSON>an was limited to files changed since baseline commit.
 • For a detailed list of skipped files and lines, run semgrep with the --verbose flag
Ran 1 rule on 1 file: 1 finding.
