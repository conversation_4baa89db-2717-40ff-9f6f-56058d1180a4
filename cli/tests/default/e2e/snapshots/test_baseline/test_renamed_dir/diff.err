

┌─────────────┐
│ Scan Status │
└─────────────┘
  Scanning 2 files tracked by git with 1 Code rule:
  Scanning 2 files.
  Current version has 2 findings.

Creating git worktree from 'HEAD^' to scan baseline.
  Will report findings introduced by these commits (may be incomplete for shallow checkouts):
    * 94d55d4 commit #2



┌─────────────┐
│ Scan Status │
└─────────────┘
  Scanning 2 files tracked by git with 1 Code rule:
  Scanning 2 files.


┌──────────────┐
│ Scan Summary │
└──────────────┘
✅ Scan completed successfully.
 • Findings: 0 (0 blocking)
 • Rules run: 1
 • Targets scanned: 2
 • Parsed lines: ~100.0%
 • <PERSON>an was limited to files changed since baseline commit.
 • For a detailed list of skipped files and lines, run semgrep with the --verbose flag
Ran 1 rule on 2 files: 0 findings.
