{"errors": [], "paths": {"scanned": ["targets/aliengrep/begin-end.log"]}, "results": [{"check_id": "rules.aliengrep.begin-end", "end": {"col": 18, "line": 1, "offset": 17}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "found something", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/begin-end.log", "start": {"col": 1, "line": 1, "offset": 0}}, {"check_id": "rules.aliengrep.begin-end", "end": {"col": 28, "line": 3, "offset": 46}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "found something", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/begin-end.log", "start": {"col": 7, "line": 3, "offset": 25}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}