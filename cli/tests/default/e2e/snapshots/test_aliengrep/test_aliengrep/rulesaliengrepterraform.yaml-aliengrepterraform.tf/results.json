{"errors": [], "paths": {"scanned": ["targets/aliengrep/terraform.tf"]}, "results": [{"check_id": "rules.aliengrep.all-origins-allowed", "end": {"col": 28, "line": 9, "offset": 241}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "CORS rule on bucket permits any origin", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/terraform.tf", "start": {"col": 5, "line": 9, "offset": 218}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}