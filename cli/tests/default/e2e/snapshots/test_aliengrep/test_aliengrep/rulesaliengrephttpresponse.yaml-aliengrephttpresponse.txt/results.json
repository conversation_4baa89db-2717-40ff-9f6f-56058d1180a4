{"errors": [], "paths": {"scanned": ["targets/aliengrep/httpresponse.txt"]}, "results": [{"check_id": "rules.aliengrep.content-type-text-html", "end": {"col": 24, "line": 6, "offset": 169}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "Detected text/html", "metadata": {}, "severity": "INFO", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/httpresponse.txt", "start": {"col": 1, "line": 6, "offset": 146}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}