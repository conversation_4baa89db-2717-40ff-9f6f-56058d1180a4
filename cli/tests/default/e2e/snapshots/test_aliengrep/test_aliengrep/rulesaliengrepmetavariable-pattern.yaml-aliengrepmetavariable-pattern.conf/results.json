{"errors": [], "paths": {"scanned": ["targets/aliengrep/metavariable-pattern.conf"]}, "results": [{"check_id": "rules.aliengrep.test-aliengrep-mv", "end": {"col": 12, "line": 1, "offset": 11}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "found 'value'\n", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/metavariable-pattern.conf", "start": {"col": 7, "line": 1, "offset": 6}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}