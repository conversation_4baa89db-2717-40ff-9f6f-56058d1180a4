{"errors": [], "paths": {"scanned": ["targets/aliengrep/dockerfile"]}, "results": [{"check_id": "rules.aliengrep.double-root", "end": {"col": 10, "line": 3, "offset": 43}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "'USER' is specified twice\n", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/dockerfile", "start": {"col": 1, "line": 3, "offset": 34}}, {"check_id": "rules.aliengrep.double-root", "end": {"col": 10, "line": 7, "offset": 111}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "'USER' is specified twice\n", "metadata": {}, "severity": "ERROR", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/dockerfile", "start": {"col": 1, "line": 7, "offset": 102}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}