{"errors": [], "paths": {"scanned": ["targets/aliengrep/html.mustache"]}, "results": [{"check_id": "rules.aliengrep.var-in-script-tag", "end": {"col": 32, "line": 24, "offset": 918}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "Detected template variable in script tag. This is dangerous because\nHTML escaping does not prevent injection in script contexts.\n", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/html.mustache", "start": {"col": 19, "line": 24, "offset": 905}}, {"check_id": "rules.aliengrep.var-in-script-tag", "end": {"col": 32, "line": 29, "offset": 1033}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "Detected template variable in script tag. This is dangerous because\nHTML escaping does not prevent injection in script contexts.\n", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/html.mustache", "start": {"col": 19, "line": 29, "offset": 1020}}, {"check_id": "rules.aliengrep.var-in-script-tag", "end": {"col": 34, "line": 32, "offset": 1135}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "Detected template variable in script tag. This is dangerous because\nHTML escaping does not prevent injection in script contexts.\n", "metadata": {}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/html.mustache", "start": {"col": 20, "line": 32, "offset": 1121}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}