{"errors": [], "paths": {"scanned": ["targets/aliengrep/nosem.html"]}, "results": [{"check_id": "var-in-href", "end": {"col": 27, "line": 15, "offset": 479}, "extra": {"engine_kind": "OSS", "fingerprint": "0x42", "lines": "requires login", "message": "Detected a template variable used in an anchor tag with the 'href' attribute. This allows a malicious actor to input the 'javascript:' URI and is subject to cross- site scripting (XSS) attacks. If using Flask, use 'url_for()' to safely generate a URL. If using Django, use the 'url' filter to safely generate a URL. If using Mustache, use a URL encoding library, or prepend a slash '/' to the variable for relative links (`href=\"/{{link}}\"`). You may also consider setting the Content Security Policy (CSP) header.", "metadata": {"cwe": "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')", "owasp": "A7: Cross-site Scripting (XSS)", "references": ["https://flask.palletsprojects.com/en/1.1.x/security/#cross-site-scripting-xss#:~:text=javascript:%20URI", "https://docs.djangoproject.com/en/3.1/ref/templates/builtins/#url", "https://github.com/pugjs/pug/issues/2952", "https://content-security-policy.com/"]}, "severity": "WARNING", "validation_state": "NO_VALIDATOR"}, "path": "targets/aliengrep/nosem.html", "start": {"col": 8, "line": 15, "offset": 460}}], "skipped_rules": [], "time": "<masked in tests>", "version": "0.42"}