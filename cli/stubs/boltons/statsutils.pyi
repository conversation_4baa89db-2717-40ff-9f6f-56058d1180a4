from typing import Any, Callable, List, Optional, Tuple, Type, Union

def format_histogram_counts(
    bin_counts: List[Tuple[float, int]],
    width: Optional[int] = ...,
    format_bin: Optional[Callable] = ...,
) -> str: ...

class Stats:
    def __init__(
        self,
        data: Union[List[int], List[float], range],
        default: float = ...,
        use_copy: bool = ...,
        is_sorted: bool = ...,
    ) -> None: ...
    def _calc_min(self) -> float: ...
    def _get_bin_bounds(
        self, count: Optional[int] = ..., with_max: bool = ...
    ) -> List[float]: ...
    def _get_pow_diffs(self, power: int) -> List[float]: ...
    @staticmethod
    def _get_quantile(
        sorted_data: Union[List[int], List[float]], q: float
    ) -> float: ...
    def _get_sorted_data(self) -> Union[List[int], List[float]]: ...
    def format_histogram(
        self, bins: Optional[Union[List[float], int]] = ..., **kw: Any
    ) -> str: ...
    def get_histogram_counts(
        self, bins: Optional[Union[List[float], int]] = ..., **kw: Any
    ) -> List[Tuple[float, int]]: ...
    def get_quantile(self, q: float) -> float: ...

class _StatsProperty:
    def __get__(
        self, obj: Optional[Stats], objtype: Optional[Type[Stats]] = ...
    ) -> Union[_StatsProperty, float]: ...
