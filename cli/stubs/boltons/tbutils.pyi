from typing import Any, Dict, List, Optional, Type, Union

def _format_final_exc_line(etype: str, value: ValueError) -> str: ...
def _some_str(value: Exception) -> str: ...
def fix_print_exception() -> None: ...
def format_exception_only(etype: Type[ValueError], value: ValueError) -> List[str]: ...

class Callpoint:
    def __init__(
        self,
        module_name: str,
        module_path: str,
        func_name: str,
        lineno: int,
        lasti: int,
        line: Optional[_DeferredLine] = ...,
    ) -> None: ...
    def __repr__(self) -> str: ...
    @classmethod
    def from_current(cls, level: int = ...) -> ContextualCallpoint: ...
    def tb_frame_str(self) -> str: ...
    def to_dict(self) -> Dict[str, Union[int, str]]: ...

class ContextualCallpoint:
    def __init__(self, *a: Any, **kw: Any) -> None: ...
    def _populate_context_lines(self, pivot: int = ...) -> None: ...
    def _populate_local_reprs(self, f_locals: Dict[str, Any]) -> None: ...
    def to_dict(
        self,
    ) -> Dict[
        str, Union[str, int, Dict[str, str], List[Dict[str, Union[int, str]]]]
    ]: ...

class ExceptionInfo:
    def __init__(self, exc_type: str, exc_msg: str, tb_info: TracebackInfo) -> None: ...
    @classmethod
    def from_current(cls) -> ExceptionInfo: ...
    def get_formatted(self) -> str: ...

class ParsedException:
    def __init__(
        self,
        exc_type_name: str,
        exc_msg: str,
        frames: Optional[List[Dict[str, str]]] = ...,
    ) -> None: ...
    def __repr__(self) -> str: ...
    @classmethod
    def from_string(cls, tb_str: str) -> ParsedException: ...
    def to_string(self) -> str: ...

class TracebackInfo:
    def __init__(
        self, frames: Union[List[ContextualCallpoint], List[Callpoint]]
    ) -> None: ...
    def __str__(self) -> str: ...
    def get_formatted(self) -> str: ...

class _DeferredLine:
    def __init__(
        self, filename: str, lineno: int, module_globals: None = ...
    ) -> None: ...
    def __len__(self) -> int: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
