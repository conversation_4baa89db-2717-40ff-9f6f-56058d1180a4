from socket import AddressFamily, SocketKind, socket
from typing import Any, Optional

class BufferedSocket:
    def __init__(
        self,
        sock: socket,
        timeout: Any = ...,
        maxsize: int = ...,
        recvsize: Any = ...,
    ) -> None: ...
    def buffer(self, data: bytes) -> None: ...
    def close(self) -> None: ...
    @property
    def family(self) -> AddressFamily: ...
    def fileno(self) -> int: ...
    def flush(self) -> None: ...
    def getpeername(self) -> str: ...
    def getrecvbuffer(self) -> bytes: ...
    def getsendbuffer(self) -> bytes: ...
    def getsockname(self) -> str: ...
    def gettimeout(self) -> Optional[float]: ...
    @property
    def proto(self) -> int: ...
    def recv(self, size: int, flags: int = ..., timeout: Any = ...) -> bytes: ...
    def recv_close(self, timeout: Any = ..., maxsize: Optional[int] = ...) -> bytes: ...
    def recv_size(self, size: int, timeout: Any = ...) -> bytes: ...
    def recv_until(
        self,
        delimiter: bytes,
        timeout: Any = ...,
        maxsize: Any = ...,
        with_delimiter: bool = ...,
    ) -> bytes: ...
    def send(self, data: bytes, flags: int = ..., timeout: Any = ...) -> int: ...
    def sendall(self, data: bytes, flags: int = ..., timeout: Any = ...) -> int: ...
    def setblocking(self, blocking: bool) -> None: ...
    def settimeout(self, timeout: float) -> None: ...
    def shutdown(self, how: int) -> None: ...
    @property
    def type(self) -> SocketKind: ...

class MessageTooLong:
    def __init__(
        self,
        bytes_read: Optional[int] = ...,
        delimiter: Optional[bytes] = ...,
    ) -> None: ...

class NetstringMessageTooLong:
    def __init__(self, size: int, maxsize: int) -> None: ...

class NetstringSocket:
    def __init__(
        self, sock: socket, timeout: int = ..., maxsize: int = ...
    ) -> None: ...
    def read_ns(self, timeout: Any = ..., maxsize: Any = ...) -> bytes: ...
    def setmaxsize(self, maxsize: int) -> None: ...
    def settimeout(self, timeout: float) -> None: ...
    def write_ns(self, payload: bytes) -> None: ...

class Timeout:
    def __init__(self, timeout: float, extra: str = ...) -> None: ...
