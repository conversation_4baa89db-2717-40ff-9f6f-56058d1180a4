from itertools import chain, islice
from typing import Any, List, Optional, Tuple, Union

class BarrelList:
    def __delitem__(self, index: slice) -> None: ...
    def __getitem__(self, index: Union[int, slice]) -> Union[int, BarrelList]: ...
    def __init__(
        self, iterable: Optional[Union[List[int], range, islice]] = ...
    ) -> None: ...
    def __iter__(self) -> chain: ...
    def __len__(self) -> int: ...
    def __reversed__(self) -> chain: ...
    def __setitem__(self, index: slice, item: range) -> None: ...
    def _balance_list(self, list_idx: int) -> bool: ...
    @property
    def _cur_size_limit(self) -> int: ...
    def _translate_index(self, index: int) -> Tuple[int, int]: ...
    def del_slice(self, start: int, stop: int, step: Optional[int] = ...) -> None: ...
    def extend(self, iterable: Union[List[int], range, islice]) -> None: ...
    @classmethod
    def from_iterable(cls, it: islice) -> BarrelList: ...
    def insert(self, index: int, item: int) -> None: ...
    def iter_slice(
        self, start: int, stop: int, step: Optional[int] = ...
    ) -> islice: ...
    def pop(self, *a: Any) -> int: ...
    def sort(self) -> None: ...

class SplayList:
    def shift(self, item_index: int, dest_index: int = ...) -> None: ...
    def swap(self, item_index: int, dest_index: int) -> None: ...
